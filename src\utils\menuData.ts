import { v4 as uuidv4 } from "uuid";
import type { MenuDataItem } from "~@/layouts/basic-layout/typing";

// ~/constants/menu.ts
export const STATIC_MENUS = [
  {
    component: "/profile/basic",
    hideInBreadcrumb: undefined,
    hideInMenu: true,
    icon: "UserOutlined",
    id: uuidv4(),
    locale: "menu.profile.basic",
    name: undefined,
    parentName: undefined,
    path: "/profile/basic",
    redirect: undefined,
    title: "ProfileBasic",
  },
  {
    component: "/account/settings",
    hideInBreadcrumb: undefined,
    hideInMenu: true,
    icon: "SettingOutlined",
    id: uuidv4(),
    locale: "menu.account.settings",
    name: "AccountSetting",
    parentName: undefined,
    path: "/account/settings",
    redirect: undefined,
    title: "AccountSetting",
  },
] as const;

export const menuLists = [
  {
    name: "Dashboard",
    icon: "HomeOutlined",
    path: "/",
    component: "/dashboard",
    redirect: "/dashboard/ps/attendance",
    parentName: null,
    hideInMenu: false,
    title: "Dashboard",
    hideInBreadcrumb: false,
    hideChildrenInMenu: false,
    locale: "menu.dashboard",
    displayOrder: 1,
    isLeaf: false,
  },
  {
    name: "PersonalAttendanceManagement",
    icon: "CalendarOutlined",
    path: "/ps-attendance-mng",
    component: "/person-attendance-management",
    redirect: null,
    parentName: null,
    hideInMenu: false,
    title: "HolidaySetting",
    hideInBreadcrumb: false,
    hideChildrenInMenu: false,
    locale: "menu.ps-attendance-mng",
    displayOrder: 2,
    isLeaf: false,
  },
  {
    name: "DashboardManager",
    icon: "ControlOutlined",
    path: "/dashboard/manager",
    component: "/dashboard/manager",
    redirect: null,
    parentName: "Dashboard",
    hideInMenu: false,
    title: "DashboardWorkplace",
    hideInBreadcrumb: false,
    hideChildrenInMenu: false,
    locale: "menu.dashboard.manager",
    displayOrder: 3,
    isLeaf: true,
  },
  {
    name: "DashboardPsAttendance",
    icon: "ScheduleOutlined",
    path: "/dashboard/ps/attendance",
    component: "/dashboard/person/attendance",
    redirect: null,
    parentName: "Dashboard",
    hideInMenu: false,
    title: "DashboardPersonAttendance",
    hideInBreadcrumb: false,
    hideChildrenInMenu: false,
    locale: "menu.dashboard.ps.attendance",
    displayOrder: 4,
    isLeaf: true,
  },
  {
    name: "ProjectManagement",
    icon: "SolutionOutlined",
    path: "/project-management",
    component: "/project-management",
    redirect: null,
    parentName: null,
    hideInMenu: false,
    title: "ProjectManagement",
    hideInBreadcrumb: false,
    hideChildrenInMenu: false,
    locale: "menu.project-management",
    displayOrder: 5,
    isLeaf: false,
  },
  {
    name: "DashboardCalendar",
    icon: "CalendarOutlined",
    path: "/dashboard/ps/calendar",
    component: "/person-attendance-management/monthly",
    redirect: null,
    parentName: "Dashboard",
    hideInMenu: true,
    title: "DashboardCalendar",
    hideInBreadcrumb: false,
    hideChildrenInMenu: false,
    locale: "menu.dashboard.ps.calendar",
    displayOrder: 6,
    isLeaf: true,
  },
  {
    name: "ProjectAttendanceManagement",
    icon: "SolutionOutlined",
    path: "/project-mng/attendance-management",
    component: "/project-management/attendance-management",
    redirect: null,
    parentName: "ProjectManagement",
    hideInMenu: false,
    title: "Orgization",
    hideInBreadcrumb: false,
    hideChildrenInMenu: false,
    locale: "menu.project-mng.attendance-management",
    displayOrder: 7,
    isLeaf: false,
  },
  {
    name: "ProjectTimecardManagement",
    icon: "ClockCircleOutlined",
    path: "/project-mng/attendance-mng/timecard",
    component: "/project-management/attendance-management/timecard",
    redirect: null,
    parentName: "ProjectAttendanceManagement",
    hideInMenu: false,
    title: "Struct",
    hideInBreadcrumb: false,
    hideChildrenInMenu: false,
    locale: "menu.project-mng.attendance-mng.timecard",
    displayOrder: 8,
    isLeaf: true,
  },
  {
    name: "ProjectRequestManagement",
    icon: "FormOutlined",
    path: "/project-mng/attendance-mng/request",
    component: "/project-management/attendance-management/request",
    redirect: null,
    parentName: "ProjectAttendanceManagement",
    hideInMenu: false,
    title: "Project",
    hideInBreadcrumb: false,
    hideChildrenInMenu: false,
    locale: "menu.project-mng.attendance-mng.request",
    displayOrder: 9,
    isLeaf: true,
  },
  {
    name: "ProjectCostManagement",
    icon: "DollarOutlined",
    path: "/project-mng/cost-management",
    component: "/project-management/cost-management",
    redirect: null,
    parentName: "ProjectManagement",
    hideInMenu: false,
    title: "WorkShift",
    hideInBreadcrumb: false,
    hideChildrenInMenu: false,
    locale: "menu.project-mng.cost-management",
    displayOrder: 10,
    isLeaf: false,
  },
  {
    name: "ProjectCostSummary",
    icon: "BarChartOutlined",
    path: "/project-mng/cost-mng/summary/:id",
    component: "/project-management/cost-management/summary",
    redirect: null,
    parentName: "ProjectCostManagement",
    hideInMenu: false,
    title: "Office",
    hideInBreadcrumb: false,
    hideChildrenInMenu: false,
    locale: "menu.project-mng.cost-summary",
    displayOrder: 11,
    isLeaf: true,
  },
  {
    name: "ProjectCostSummaryDetail",
    icon: "FundOutlined",
    path: "/project-mng/cost-mng/summary-detail",
    component: "/project-management/cost-management/summary-detail",
    redirect: null,
    parentName: "ProjectCostManagement",
    hideInMenu: false,
    title: "Position",
    hideInBreadcrumb: false,
    hideChildrenInMenu: false,
    locale: "menu.project-mng.cost-mng.summary-detail",
    displayOrder: 12,
    isLeaf: true,
  },
  {
    name: "ProjectCostSimulationReport",
    icon: "ReconciliationOutlined",
    path: "/project-mng/cost-mng/simulation-report",
    component: "/administrative-office/projects/construction-cost",
    redirect: null,
    parentName: "ProjectCostManagement",
    hideInMenu: false,
    title: "Calendar",
    hideInBreadcrumb: false,
    hideChildrenInMenu: false,
    locale: "menu.project-mng.cost-mng.simulation-report",
    displayOrder: 13,
    isLeaf: true,
  },
  {
    name: "PersonalMonthlyAttendance",
    icon: "CalendarOutlined",
    path: "/ps-attendance-mng/monthly",
    component: "/person-attendance-management/monthly",
    redirect: null,
    parentName: "PersonalAttendanceManagement",
    hideInMenu: false,
    title: "WorksiteMonitor",
    hideInBreadcrumb: false,
    hideChildrenInMenu: false,
    locale: "menu.ps-attendance-mng.monthly",
    displayOrder: 14,
    isLeaf: true,
  },
  {
    name: "PersonalRequestManagement",
    icon: "FileDoneOutlined",
    path: "/ps-attendance-mng/request",
    component: "/person-attendance-management/request",
    redirect: null,
    parentName: "PersonalAttendanceManagement",
    hideInMenu: false,
    title: "WorksiteManager",
    hideInBreadcrumb: false,
    hideChildrenInMenu: false,
    locale: "menu.ps-attendance-mng.request",
    displayOrder: 15,
    isLeaf: true,
  },
  {
    name: "EmployeeManagement",
    icon: "TeamOutlined",
    path: "/administrative-office/employees",
    component: null,
    redirect: null,
    parentName: "AdministrativeOffice",
    hideInMenu: false,
    title: "DayOffRequest",
    hideInBreadcrumb: false,
    hideChildrenInMenu: false,
    locale: "menu.administrative-office.employees",
    displayOrder: 16,
    isLeaf: false,
  },

  {
    name: "AdministrativeOffice",
    icon: "SettingOutlined",
    path: "/administrative-offfice",
    component: null,
    redirect: null,
    parentName: null,
    hideInMenu: false,
    title: "AdministrativeOffice",
    hideInBreadcrumb: false,
    hideChildrenInMenu: false,
    locale: "menu.administrative-office",
    displayOrder: 17,
    isLeaf: false,
  },
  {
    name: "EmployeeList",
    icon: "UnorderedListOutlined",
    path: "/administrative-office/employees/list",
    component: "/administrative-office/employees/list",
    redirect: "",
    parentName: "EmployeeManagement",
    hideInMenu: false,
    title: "EmployeeList",
    hideInBreadcrumb: false,
    hideChildrenInMenu: false,
    locale: "menu.administrative-office.employees.list",
    displayOrder: 18,
    isLeaf: true,
  },
  {
    name: "EmployeePaidLeave",
    icon: "CoffeeOutlined",
    path: "/administrative-office/employees/paid-leave",
    component: "/administrative-office/employees/paid-leave",
    redirect: "",
    parentName: "EmployeeManagement",
    hideInMenu: false,
    title: "WorksiteAttendanceRequestManagement",
    hideInBreadcrumb: false,
    hideChildrenInMenu: false,
    locale: "menu.administrative-office.employees.paid-leave",
    displayOrder: 19,
    isLeaf: true,
  },
  {
    name: "EmployeeMonthClosing",
    icon: "ScheduleOutlined",
    path: "/administrative-office/employees/month-closing",
    component: "/administrative-office/employees/month-closing",
    redirect: "",
    parentName: "EmployeeManagement",
    hideInMenu: false,
    title: "OfficeAttendanceRequestManagement",
    hideInBreadcrumb: false,
    hideChildrenInMenu: false,
    locale: "menu.administrative-office.employees.month-closing",
    displayOrder: 20,
    isLeaf: true,
  },
  {
    name: "ProjectSettings",
    icon: "ProjectOutlined",
    path: "/administrative-office/projects",
    component: null,
    redirect: null,
    parentName: "AdministrativeOffice",
    hideInMenu: false,
    title: "RoleManagement",
    hideInBreadcrumb: false,
    hideChildrenInMenu: false,
    locale: "menu.administrative-office.projects",
    displayOrder: 21,
    isLeaf: false,
  },
  {
    name: "ProjectList",
    icon: "ProjectOutlined",
    path: "/administrative-office/projects/list",
    component: "/administrative-office/projects/list",
    redirect: null,
    parentName: "ProjectSettings",
    hideInMenu: false,
    title: "RoleDecentralization",
    hideInBreadcrumb: false,
    hideChildrenInMenu: false,
    locale: "menu.administrative-office.projects.list",
    displayOrder: 22,
    isLeaf: true,
  },
  {
    name: "ProjectSchedule",
    icon: "CalendarOutlined",
    path: "/administrative-office/projects/schedule",
    component: "/administrative-office/projects/schedule",
    redirect: null,
    parentName: "ProjectSettings",
    hideInMenu: false,
    title: "WorksiteManagement",
    hideInBreadcrumb: false,
    hideChildrenInMenu: false,
    locale: "menu.administrative-office.projects.schedule",
    displayOrder: 23,
    isLeaf: true,
  },
  {
    name: "ProjectWorkShift",
    icon: "SwapOutlined",
    path: "/administrative-office/projects/work-shift",
    component: "/administrative-office/projects/work-shift",
    redirect: null,
    parentName: "ProjectSettings",
    hideInMenu: false,
    title: "InputCost",
    hideInBreadcrumb: false,
    hideChildrenInMenu: false,
    locale: "menu.administrative-office.projects.work-shift",
    displayOrder: 27,
    isLeaf: true,
  },
  {
    name: "ProjectHumanCost",
    icon: "DollarOutlined",
    path: "/administrative-office/projects/human-cost",
    component: "/administrative-office/projects/human-cost",
    redirect: null,
    parentName: "ProjectSettings",
    hideInMenu: false,
    title: "CostItem",
    hideInBreadcrumb: false,
    hideChildrenInMenu: false,
    locale: "menu.administrative-office.projects.human-cost",
    displayOrder: 28,
    isLeaf: true,
  },
  {
    name: "PartnerManagement",
    icon: "TeamOutlined",
    path: "/administrative-office/partners",
    component: null,
    redirect: null,
    parentName: "AdministrativeOffice",
    hideInMenu: false,
    title: "CostCategory",
    hideInBreadcrumb: false,
    hideChildrenInMenu: false,
    locale: "menu.administrative-office.partners",
    displayOrder: 29,
    isLeaf: false,
  },
  {
    name: "PartnerList",
    icon: "ApartmentOutlined",
    path: "/administrative-office/partners/list",
    component: "/administrative-office/partners/list",
    redirect: null,
    parentName: "PartnerManagement",
    hideInMenu: false,
    title: "Vendor",
    hideInBreadcrumb: false,
    hideChildrenInMenu: false,
    locale: "menu.administrative-office.partners.list",
    displayOrder: 30,
    isLeaf: true,
  },
  {
    name: "PartnerVendor",
    icon: "AppstoreOutlined",
    path: "/administrative-office/partners/vendor",
    component: "/administrative-office/partners/vendor",
    redirect: null,
    parentName: "PartnerManagement",
    hideInMenu: true,
    title: "PartnerVendor",
    hideInBreadcrumb: false,
    hideChildrenInMenu: false,
    locale: "menu.administrative-office.partners.vendor",
    displayOrder: 30,
    isLeaf: true,
  },
  {
    name: "PartnerManufacturer",
    icon: "BuildOutlined",
    path: "/administrative-office/partners/manufacturer",
    component: "/administrative-office/partners/manufacturer",
    redirect: null,
    parentName: "PartnerManagement",
    hideInMenu: true,
    title: "PartnersManufacturer",
    hideInBreadcrumb: false,
    hideChildrenInMenu: false,
    locale: "menu.administrative-office.partners.manufacturer",
    displayOrder: 30,
    isLeaf: true,
  },
  {
    name: "PartnerInvoiceManagement",
    icon: "FileTextOutlined",
    path: "/administrative-office/partners/invoice",
    component: "/administrative-office/partners/invoice",
    redirect: null,
    parentName: "PartnerManagement",
    hideInMenu: false,
    title: "CostManagementDashboard",
    hideInBreadcrumb: false,
    hideChildrenInMenu: false,
    locale: "menu.administrative-office.partners.invoice",
    displayOrder: 31,
    isLeaf: true,
  },
  {
    name: "TransactionItemManagement",
    icon: "ProfileOutlined",
    path: "/administrative-office/transaction-items",
    component: null,
    redirect: null,
    parentName: "AdministrativeOffice",
    hideInMenu: false,
    title: "WorkplaceCostManagement",
    hideInBreadcrumb: false,
    hideChildrenInMenu: false,
    locale: "menu.administrative-office.transaction-items",
    displayOrder: 32,
    isLeaf: false,
  },
  {
    name: "TransactionItemList",
    icon: "UnorderedListOutlined",
    path: "/administrative-office/transaction-items/list",
    component: "/administrative-office/transaction-items/list",
    redirect: null,
    parentName: "TransactionItemManagement",
    hideInMenu: false,
    title: "OfficeAttendanceRequestDetail",
    hideInBreadcrumb: false,
    hideChildrenInMenu: false,
    locale: "menu.administrative-office.transaction-items.list",
    displayOrder: 33,
    isLeaf: true,
  },
  {
    name: "TransactionItemSettings",
    icon: "SettingOutlined",
    path: "/administrative-office/transaction-items/category",
    component: "/administrative-office/transaction-items/settings",
    redirect: null,
    parentName: "TransactionItemManagement",
    hideInMenu: false,
    title: "LeaveSetting",
    hideInBreadcrumb: false,
    hideChildrenInMenu: false,
    locale: "menu.administrative-office.transaction-items.settings",
    displayOrder: 34,
    isLeaf: true,
  },
  {
    name: "NotificationManagement",
    icon: "MessageOutlined",
    path: "/administrative-office/notification",
    component: null,
    redirect: null,
    parentName: "TransactionItemManagement",
    hideInMenu: false,
    title: "CostConfig",
    hideInBreadcrumb: false,
    hideChildrenInMenu: false,
    locale: "menu.administrative-office.notification",
    displayOrder: 35,
    isLeaf: false,
  },
  {
    name: "NotificationSettings",
    icon: "BellOutlined",
    path: "/administrative-office/notification/settings",
    component: "/administrative-office/notification/settings",
    redirect: null,
    parentName: "0194a5f7-c5df-7e5f-9b46-c4c0d9d2e4ee",
    hideInMenu: false,
    title: "AdministrativeOfficeNotifications",
    hideInBreadcrumb: false,
    hideChildrenInMenu: false,
    locale: "menu.administrative-office.notification.settings",
    displayOrder: 36,
    isLeaf: true,
  },
  {
    name: "NotificationCalendar",
    icon: "CalendarOutlined",
    path: "/administrative-office/notification/calendar",
    component: "/administrative-office/notification/calendar",
    redirect: null,
    parentName: "0194a5f7-c5df-7e5f-9b46-c4c0d9d2e4ee",
    hideInMenu: false,
    title: "AdministrativeOfficeNotifications",
    hideInBreadcrumb: false,
    hideChildrenInMenu: false,
    locale: "menu.administrative-office.notification.calendar",
    displayOrder: 37,
    isLeaf: true,
  },
  {
    name: "GeneralSettings",
    icon: "CalendarOutlined",
    path: "/setting",
    component: null,
    redirect: null,
    parentName: null,
    hideInMenu: false,
    title: "Setting",
    hideInBreadcrumb: false,
    hideChildrenInMenu: false,
    locale: "menu.setting",
    displayOrder: 39,
    isLeaf: false,
  },
  {
    name: "OrganizationSettings",
    icon: "ClusterOutlined",
    path: "/settings/organization",
    component: "/settings/organization",
    redirect: null,
    parentName: "GeneralSettings",
    hideInMenu: false,
    title: "SettingsOrganization",
    hideInBreadcrumb: false,
    hideChildrenInMenu: false,
    locale: "menu.settings.organization",
    displayOrder: 40,
    isLeaf: true,
  },
  {
    name: "RoleSettings",
    icon: "SafetyCertificateOutlined",
    path: "/settings/role",
    component: "/settings/role",
    redirect: null,
    parentName: "GeneralSettings",
    hideInMenu: false,
    title: "SettingsRole",
    hideInBreadcrumb: false,
    hideChildrenInMenu: false,
    locale: "menu.settings.role",
    displayOrder: 41,
    isLeaf: true,
  },
  {
    name: "MonthClosingDetail",
    icon: "ScheduleOutlined",
    path: "/administrative-office/employees/month-closing/detail/:id",
    component: "/administrative-office/employees/month-closing/detail",
    redirect: null,
    parentName: "EmployeeManagement",
    hideInMenu: true,
    title: "MonthClosingDetail",
    hideInBreadcrumb: true,
    hideChildrenInMenu: true,
    locale: "menu.administrative-office.employees.month-closing.detail",
    displayOrder: 42,
    isLeaf: true,
  },
  {
    name: "AutoCheckoutSetting",
    icon: "UnorderedListOutlined",
    path: "/administrative-office/employees/auto-checkout-setting",
    component: "/administrative-office/employees/auto-checkout-setting",
    redirect: null,
    parentName: "EmployeeManagement",
    hideInMenu: true,
    title: "AutoCheckoutSetting",
    hideInBreadcrumb: false,
    hideChildrenInMenu: false,
    locale: "menu.administrative-office.employees.auto-checkout-setting",
    displayOrder: 43,
    isLeaf: true,
  },

  // {
  //   id: '01958932-5ae7-76bd-84ec-92237a0238ed',
  //   name: 'DashboardAttendanceDetail',
  //   icon: 'ScheduleOutlined',
  //   path: '/dashboard/attendance-detail',
  //   component: '/dashboard/attendance-detail',
  //   redirect: null,
  //   parentName: null,
  //   hideInMenu: true,
  //   title: 'DashboardAttendanceDetail',
  //   hideInBreadcrumb: false,
  //   hideChildrenInMenu: false,
  //   locale: 'menu.dashboard.attendance.detail',
  //   displayOrder: 44,
  //   isLeaf: true,
  // },
] as MenuDataItem[];

// const menuLists = [
//   {
//     id: 1,
//     parentName: null,
//     title: "Dashboard",
//     icon: "DashboardOutlined",
//     component: "RouteView",
//     redirect: "/dashboard/workplace",
//     path: "/dashboard",
//     name: "Workplace",
//     keepAlive: true,
//     locale: "menu.dashboard",
//   },
//   {
//     id: 11,
//     parentName: 1,
//     title: "Workplace",
//     icon: "DashboardOutlined",
//     component: "/dashboard/workplace",
//     path: "/dashboard/workplace",
//     name: "Workplace",
//     keepAlive: true,
//     locale: "menu.dashboard.workplace",
//   },

//   // create timekeeping detail menu
//   {
//     id: 122,
//     title: "Timekeeping detail",
//     icon: "FieldTimeOutlined",
//     component: "/dashboard/workplace/time-keeping-detail",
//     path: "/dashboard/workplace/timekeeping-detail/:id",
//     name: "TimekeepingDetail",
//     keepAlive: true,
//     locale: 'menu.dashboard.timekeeping.detail',
//     hideInMenu: true
//   },

//   //Request ngày nghỉ
//   {
//     id: 13,
//     parentName: 1,
//     title: "Day-off request",
//     icon: "FieldTimeOutlined",
//     component: "/dashboard/day-off-request",
//     path: "/dashboard/day-off-request/",
//     name: "DayOffRequest",
//     keepAlive: true,
//     locale: "menu.dashboard.dayOffRequest",
//   },

//   {
//     id: 14,
//     parentName: 1,
//     title: "Calendar",
//     icon: "CalendarOutlined",
//     component: "/dashboard/analysis",
//     path: "/dashboard/calendar",
//     name: "Calendar",
//     keepAlive: true,
//     locale: "menu.calendar",
//   },

//   {
//     id: 2,
//     parentName: null,
//     title: "Worksite manager",
//     icon: "ClusterOutlined",
//     component: "RouteView",
//     path: "/manager",
//     name: "WorksiteManager",
//     locale: "menu.manager.worksite",
//   },

//   {
//     id: 21,
//     parentName: 2,
//     title: "TeamManager",
//     icon: "UserOutlined",
//     component: "/manager/worksite",
//     path: "/manager/worksite",
//     name: "WorksiteManager",
//     keepAlive: true,
//     locale: "menu.manager.worksite",
//   },

//   {
//     id: 22,
//     parentName: 2,
//     title: "TeamManager",
//     icon: "UserOutlined",
//     component: "/manager/office",
//     path: "/manager/office",
//     name: "OfficeManager",
//     keepAlive: true,
//     locale: "menu.manager.office",
//   },

//   {
//     id: 23,
//     parentName: 2,
//     title: "Worksite schedule management",
//     icon: "UserOutlined",
//     component: "/manager/worksite-schedule",
//     path: "/manager/worksite-schedule",
//     name: "WorksiteSchedule",
//     keepAlive: true,
//     locale: "menu.manager.worksite.schedule",
//   },

//   {
//     id: 24,
//     parentName: 2,
//     title: "Worksite list",
//     icon: "UserOutlined",
//     component: "/manager/worksite-list",
//     path: "/manager/worksite-list",
//     name: "WorksiteList",
//     keepAlive: true,
//     locale: "menu.manager.worksite.list",
//   },

//   {
//     id: 3,
//     parentName: null,
//     title: "Department management",
//     icon: "ClusterOutlined",
//     component: "RouteView",
//     redirect: "/department/member-list",
//     path: "/department",
//     name: "DepartmentManagement",
//     locale: "menu.manager.department",
//   },

//   {
//     id: 31,
//     parentName: 3,
//     title: "Member list",
//     icon: "ClusterOutlined",
//     component: "/department/member-list",
//     path: "/department/member-list",
//     name: "memberList",
//     locale: "menu.manager.department.memberList",
//   },

//   {
//     id: 32,
//     parentName: 3,
//     title: "Request management",
//     icon: "ClusterOutlined",
//     component: "/department/request-management",
//     path: "/department/request-management",
//     name: "requestManagement",
//     locale: "menu.manager.department.request",
//   },

//   {
//     id: 33,
//     parentName: 3,
//     title: "Monthly attendance closing",
//     icon: "ClusterOutlined",
//     component: "/department/monthly-attendance-closing",
//     path: "/department/monthly-attendance-closing",
//     name: "monthlyAttendanceClosing",
//     locale: "menu.manager.department.monthlyAttendanceClosing",
//   },

//   {
//     id: 4,
//     parentName: null,
//     title: "Admin",
//     icon: "HomeOutlined",
//     component: "RouteView",
//     redirect: "/company/orgization",
//     path: "/company",
//     name: "admin",
//     locale: "menu.admin",
//   },

//   {
//     id: 41,
//     parentName: 4,
//     title: "Orgization",
//     icon: "ProfileOutlined",
//     component: "/company/org",
//     path: "/company/orgization",
//     name: "Orgization",
//     keepAlive: true,
//     locale: "menu.company.org",
//   },

//   {
//     id: 42,
//     parentName: 4,
//     title: "Struct",
//     icon: "PartitionOutlined",
//     component: "/company/struct",
//     path: "/company/struct",
//     name: "Struct",
//     keepAlive: true,
//     locale: "menu.company.struct",
//   },
//   {
//     id: 43,
//     parentName: 4,
//     title: "Project",
//     icon: "ProjectOutlined",
//     component: "/company/project",
//     path: "/company/project",
//     name: "Project",
//     keepAlive: true,
//     locale: "menu.company.project",
//   },
//   {
//     id: 44,
//     parentName: 4,
//     title: "WorkShift",
//     icon: "FieldTimeOutlined",
//     component: "/company/work-shift",
//     path: "/company/work-shift",
//     name: "WorkShift",
//     keepAlive: true,
//     locale: "menu.company.work-shift",
//   },
//   {
//     id: 441,
//     title: "WorkShift Detail",
//     icon: "ProjectOutlined",
//     component: "/company/work-shift/detail",
//     path: "/company/work-shift/:id",
//     name: "WorkShiftDetail",
//     keepAlive: true,
//     locale: "menu.company.work-shift",
//     hideInMenu: true,
//   },
//   {
//     id: 45,
//     parentName: 4,
//     title: "Office",
//     icon: "BankOutlined",
//     component: "/company/office",
//     path: "/company/office",
//     name: "Office",
//     keepAlive: true,
//     locale: "menu.company.office",
//   },
//   {
//     id: 46,
//     parentName: 4,
//     title: "Role",
//     icon: "BankOutlined",
//     component: "/company/role",
//     path: "/company/role",
//     name: "Role",
//     keepAlive: true,
//     locale: "menu.company.role",
//   },

//   {
//     id: 47,
//     parentName: 4,
//     title: "Contract",
//     icon: "BankOutlined",
//     component: "/company/contract",
//     path: "/company/contract",
//     name: "Contract",
//     keepAlive: true,
//     locale: "menu.company.contract",
//   },

//   {
//     id: 48,
//     parentName: 4,
//     title: "UserManager",
//     icon: "UserOutlined",
//     component: "/manager/user",
//     path: "/manager/user",
//     name: "UserManager",
//     keepAlive: true,
//     locale: "menu.manager.user",
//   },

//   {
//     id: 15,
//     parentName: 5,
//     title: "Holiday Setting",
//     icon: "BankOutlined",
//     component: "/company/holiday-setting",
//     path: "/company/holiday-setting",
//     name: "holidaySetting",
//     keepAlive: true,
//     locale: "menu.company.holidaySetting",
//   },
//   // {
//   //   id: 28,
//   //   parentName: null,
//   //   title: "Profile",
//   //   icon: "ProfileOutlined",
//   //   component: "RouteView",
//   //   redirect: "/profile/basic",
//   //   path: "/profile",
//   //   name: "Profile",
//   //   locale: "menu.profile",
//   // },
//   // {
//   //   id: 29,
//   //   parentName: 28,
//   //   path: "/profile/basic",
//   //   title: "Profile Basic",
//   //   name: "ProfileBasic",
//   //   component: "/profile/basic/index",
//   //   locale: "menu.profile.basic",
//   // },

//   // {
//   //   id: 35,
//   //   parentName: 28,
//   //   path: "/profile/advanced",
//   //   title: "Profile Advanced",
//   //   name: "ProfileAdvanced",
//   //   component: "/profile/advanced/index",
//   //   locale: "menu.profile.advanced",
//   // },

//   // {
//   //   id: 36,
//   //   parentName: null,
//   //   title: "Account",
//   //   icon: "UserOutlined",
//   //   component: "RouteView",
//   //   redirect: "/account/center",
//   //   path: "/account",
//   //   name: "Account",
//   //   locale: "menu.account",
//   // },
//   // {
//   //   id: 37,
//   //   parentName: 36,
//   //   path: "/account/center",
//   //   title: "Account Center ",
//   //   name: "AccountCenter",
//   //   component: "/account/center",
//   //   locale: "menu.account.center",
//   // },
//   // {
//   //   id: 38,
//   //   parentName: 36,
//   //   path: "/account/settings",
//   //   title: "Account Settings",
//   //   name: "AccountSettings",
//   //   component: "/account/settings",
//   //   locale: "menu.account.settings",
//   // },
//   {
//     id: 19,
//     name: "Schedule",
//     title: "Schedule",
//     locale: "menu.company.schedule",
//     path: "/company/schedule",
//     redirect: "/company/schedule/office",
//     icon: "FieldTimeOutlined",
//   },
//   {
//     id: 25,
//     name: "ScheduleOffice",
//     title: "ScheduleOffice",
//     locale: "menu.company.schedule.office",
//     path: "/company/schedule/office",
//     component: "/company/schedule/office",
//     icon: "FieldTimeOutlined",
//     parentName: 19,
//   },
//   {
//     id: 16,
//     name: "ScheduleProject",
//     title: "ScheduleProject",
//     locale: "menu.company.schedule.project",
//     path: "/company/schedule/project",
//     component: "/company/schedule/project",
//     icon: "FieldTimeOutlined",
//     parentName: 19,
//   },
// ];

// export default menuLists;
