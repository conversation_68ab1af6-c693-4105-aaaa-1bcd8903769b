<script lang="ts" setup>
import type { TablePaginationConfig } from 'ant-design-vue';
import { message } from 'ant-design-vue';
import {
  ArrowRightOutlined,
  DownOutlined,
  LeftOutlined,
  PlusOutlined,
  RightOutlined,
} from '@ant-design/icons-vue';
import _, { cloneDeep } from 'lodash';
import type {
  ColumnGroupType,
  ColumnType,
  FilterValue,
} from 'ant-design-vue/es/table/interface';
import { usePagination } from 'vue-request';
import dayjs from 'dayjs';
import type { SelectValue } from 'ant-design-vue/es/select';
import type { RequestItem } from '~@/api/dashboard/date-off-request';
import {
  createNewRequestApi,
  deleteRequest,
  getOneRequest,
  getRequest,
  updateRequestApi,
} from '~@/api/dashboard/date-off-request';
import type { SystemStatusEnumKey } from '~@/enums/system-status-enum';
import {
  ModalType,
  RequestStatus,
  RequestStatusEnum,
  RequestType,
  RequestTypeEnum,
  SelectTimeEnum,
  TimeFrameEnum,
} from '~@/enums/system-status-enum'
import { ResponseStatusEnum } from '~@/enums/response-status-enum'
import logger from '~@/utils/logger'
import type {
  LeaveTypeItem,
  RequestTypeItem,
} from '~@/api/common/common'
import {
  getLeaveTypeList,
  getRequestTypeList,
} from '~@/api/common/common'
import type {
  EmployeeItem,
} from '~@/api/employee/employee'
import {
  getEmployeeApprovalAuthority,
} from '~@/api/employee/employee'

type ColumnItemType<T> = ColumnGroupType<T> | ColumnType<T>;

interface Params {
  pageNum?: number;
  pageSize?: number;
  keyword?: string;
  statusCode?: string[];
  fromDate?: string;
  toDate?: string;
  date?: string;
}

type FormState = RequestItem & {
  selectTime?: string;
  project?: {
    value: string;
    label: string;
  };
  requestType?: {
    value: string;
    label: string;
    option?: {
      requiredLevel1Approval: boolean;
      requiredLevel2Approval: boolean;
    };
  };
  leaveType?: {
    value: string;
    label: string;
  };
  halfDay?: string | dayjs.Dayjs;
  day?: string | dayjs.Dayjs;
  timeFrame?: string;
  fromDate?: string | dayjs.Dayjs;
  fromTime?: string | dayjs.Dayjs;
  toDate?: string | dayjs.Dayjs;
  toTime?: string | dayjs.Dayjs;
};

const initFormState: FormState = {
  requestId: '',
  description: '',
  requestType: undefined,
  leaveType: undefined,
  project: undefined,
  fromDate: undefined,
  fromTime: undefined,
  toDate: undefined,
  toTime: undefined,
  day: undefined,
  halfDay: undefined,
  timeFrame: TimeFrameEnum.MORNING,
  selectTime: SelectTimeEnum.DAY,
};

const formRef = ref();
const modalLoading = ref<boolean>(false);
const isOpenModal = ref<boolean>(false);
const modalType = ref<ModalType>(ModalType.ADD);
const requestTypes = ref<RequestTypeItem[]>([]);
const leaveTypes = ref<LeaveTypeItem[]>([]);
const { projects } = useProjectStore();
const employees = ref<EmployeeItem[]>([]);
const isRepresentativeApprover = ref<boolean>(false);
const { t } = useI18n();
const formState = reactive<FormState>({ ...cloneDeep(initFormState) });
const searchForm = ref<Params>({
  pageSize: 10,
  pageNum: 1,
  fromDate: dayjs().startOf('month').format('YYYY-MM-DD'),
  toDate: dayjs().endOf('month').format('YYYY-MM-DD'),
});
const searchDate = ref<dayjs.Dayjs>(dayjs());

async function queryData(params?: Params) {
  const { data } = await getRequest(params);
  return data;
}

const {
  data: dataSource,
  loading,
  refresh,
  total,
  current,
  run,
  pageSize,
} = usePagination(queryData, {
  defaultParams: [searchForm.value],
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
    totalKey: 'totalRecords',
  },
});

const pagination = computed(() => ({
  total: total.value ?? 10,
  current: current.value,
  pageSize: pageSize.value,
  showSizeChanger: true,
}));

const columns = computed<ColumnItemType<RequestItem>[]>(() => {
  const isLeave = dataSource.value?.items?.some((item) => !!item.leaveTypeName);
  const isJapan = t('locale') === 'ja';
  let widthRequest = 1900;
  if (isLeave && isJapan) widthRequest = 1400;
  if (isLeave && !isJapan) widthRequest = 1900;
  if (!isLeave && isJapan) widthRequest = 950;
  if (!isLeave && !isJapan) widthRequest = 1500;

  return [
    { dataIndex: 'status', width: 120, align: 'center' },
    { dataIndex: 'request', width: widthRequest },
    { dataIndex: 'createTime', width: 150 },
    { dataIndex: 'action', width: 150, align: 'center', fixed: 'right' },
  ];
});

const selectTimeOptions = computed(() => {
  return [
    { value: SelectTimeEnum.DAY, label: t('day') },
    { value: SelectTimeEnum.HALF_DAY, label: t('half-day') },
    { value: SelectTimeEnum.CUSTOM_DAY, label: t('custom-day') },
  ];
});

const timeFrameOptions = computed(() => {
  return [
    { value: TimeFrameEnum.MORNING, label: t('morning') },
    { value: TimeFrameEnum.AFTERNOON, label: t('afternoon') },
  ];
});

function onChangeSelectTime(value: SelectValue) {
  formState.fromDate = undefined;
  formState.fromTime = undefined;
  formState.toDate = undefined;
  formState.toTime = undefined;
  formState.timeFrame = TimeFrameEnum.MORNING;
  switch (value) {
    case SelectTimeEnum.DAY:
      formState.halfDay = undefined;
      formState.day = undefined;
      break;
    case SelectTimeEnum.HALF_DAY:
      formState.halfDay = undefined;
      formState.day = undefined;
      break;
    case SelectTimeEnum.CUSTOM_DAY:
      formState.halfDay = undefined;
      formState.day = undefined;
      break;
  }
}

function onChangeDay(value: string | dayjs.Dayjs) {
  const fromDate = dayjs(value).hour(8).minute(0).second(0);
  formState.fromDate = fromDate;
  formState.fromTime = fromDate;
  const toDate = dayjs(value).hour(17).minute(0).second(0);
  formState.toDate = toDate;
  formState.toTime = toDate;
}

function onChangeHalfDay(value: string | dayjs.Dayjs) {
  switch (formState.timeFrame) {
    case TimeFrameEnum.MORNING: {
      const fromDate = dayjs(value).hour(8).minute(0).second(0);
      formState.fromDate = fromDate;
      formState.fromTime = fromDate;
      const toDate = dayjs(value).hour(12).minute(0).second(0);
      formState.toDate = toDate;
      formState.toTime = toDate;
      break;
    }
    case TimeFrameEnum.AFTERNOON: {
      const fromDate = dayjs(value).hour(13).minute(0).second(0);
      formState.fromDate = fromDate;
      formState.fromTime = fromDate;
      const toDate = dayjs(value).hour(17).minute(0).second(0);
      formState.toDate = toDate;
      formState.toTime = toDate;
      break;
    }
  }
}

function onChangeTimeFrame(value: SelectValue) {
  switch (value) {
    case TimeFrameEnum.MORNING: {
      const fromDate = dayjs(formState.fromDate).hour(8).minute(0).second(0);
      formState.fromDate = fromDate;
      formState.fromTime = fromDate;
      const toDate = dayjs(formState.fromDate).hour(12).minute(0).second(0);
      formState.toDate = toDate;
      formState.toTime = toDate;
      break;
    }
    case TimeFrameEnum.AFTERNOON: {
      const fromDate = dayjs(formState.fromDate).hour(13).minute(0).second(0);
      formState.fromDate = fromDate;
      formState.fromTime = fromDate;
      const toDate = dayjs(formState.fromDate).hour(17).minute(0).second(0);
      formState.toDate = toDate;
      formState.toTime = toDate;
      break;
    }
  }
}

const getSpanLeft = computed(() => {
  const isLeave = dataSource.value?.items?.some((item) => !!item.leaveTypeName);
  const isJapan = t('locale') === 'ja';
  if (isLeave && isJapan) return { name: 4, type: 7, date: 13 };
  if (isLeave && !isJapan) return { name: 7, type: 7, date: 10 };
  if (!isLeave && isJapan) return { name: 6, type: 0, date: 18 };
  if (!isLeave && !isJapan) return { name: 10, type: 0, date: 14 };
  return { name: 7, type: 6, date: 13 };
});

const getSpanRight = computed(() => {
  const isLeave = dataSource.value?.items?.some((item) => !!item.leaveTypeName);
  const isJapan = t('locale') === 'ja';
  if (isLeave && isJapan) return { approver: 15, author: 9 };
  if (isLeave && !isJapan) return { approver: 15, author: 9 };
  if (!isLeave && isJapan) return { approver: 13, author: 11 };
  if (!isLeave && !isJapan) return { approver: 15, author: 9 };
  return { approver: 15, author: 9 };
});

async function onFinish() {
  try {
    await formRef.value.validate();
    const fromDate = dayjs(formState.fromDate).format('YYYY-MM-DD');
    const fromTime =
      formState.fromTime && dayjs(formState.fromTime).format('HH:mm:ss');
    const toDate = dayjs(formState.toDate).format('YYYY-MM-DD');
    const toTime =
      formState.toTime && dayjs(formState.toTime).format('HH:mm:ss');

    switch (modalType.value) {
      case ModalType.ADD: {
        const create = await createNewRequestApi({
          requestFrom: `${fromDate}${fromTime ? ` ${fromTime}` : ''}`,
          requestTo: `${toDate}${toTime ? ` ${toTime}` : ''}`,
          requestTypeCode: formState.requestType?.value,
          leaveTypeCode: formState.leaveType?.value,
          projectId: formState.project?.value,
          description: formState.description,
        });
        if (create.status !== ResponseStatusEnum.SUCCESS) break;

        message.success(create.message);
        break;
      }
      case ModalType.EDIT: {
        const update = await updateRequestApi(formState.requestId, {
          requestFrom: `${fromDate}${fromTime ? ` ${fromTime}` : ''}`,
          requestTo: `${toDate}${toTime ? ` ${toTime}` : ''}`,
          requestTypeCode: formState.requestType?.value,
          leaveTypeCode: formState.leaveType?.value,
          projectId: formState.project?.value,
          description: formState.description,
        });
        if (update.status !== ResponseStatusEnum.SUCCESS) break;

        message.success(update.message);
        break;
      }
      default:
        break;
    }

    isOpenModal.value = false;
    onReset();
    refresh();
  } catch (error) {
    logger.error(error);
  }
}

function getSelectTime(requestFrom: string, requestTo: string) {
  const time: { selectTime?: SelectTimeEnum; timeFrame?: TimeFrameEnum } = {
    selectTime: SelectTimeEnum.CUSTOM_DAY,
    timeFrame: undefined,
  };
  const from = dayjs(requestFrom);
  const to = dayjs(requestTo);

  if (from.isSame(to, 'day')) {
    if (from.hour() === 8 && to.hour() === 17) {
      time.selectTime = SelectTimeEnum.DAY;
      time.timeFrame = undefined;
    } else if (from.hour() === 8 && to.hour() === 12) {
      time.selectTime = SelectTimeEnum.HALF_DAY;
      time.timeFrame = TimeFrameEnum.MORNING;
    } else if (from.hour() === 13 && to.hour() === 17) {
      time.selectTime = SelectTimeEnum.HALF_DAY;
      time.timeFrame = TimeFrameEnum.AFTERNOON;
    }
  }

  return time;
}

async function openModal(id: string, type: ModalType) {
  switch (type) {
    case ModalType.ADD:
      isRepresentativeApprover.value = false;
      modalType.value = type;
      isOpenModal.value = true;
      break;
    case ModalType.COPY:
    case ModalType.EDIT: {
      isRepresentativeApprover.value = false;
      isOpenModal.value = true;
      modalLoading.value = true;
      modalType.value = type;

      try {
        const update = await getOneRequest(id);
        formState.requestId = id;
        if (update.data?.requestFrom && update.data?.requestTo) {
          const time = getSelectTime(
            update.data?.requestFrom,
            update.data?.requestTo
          );
          switch (time.selectTime) {
            case SelectTimeEnum.DAY: {
              formState.selectTime = time.selectTime;
              formState.day = dayjs(update.data?.requestFrom);
              onChangeDay(update.data?.requestFrom);
              break;
            }
            case SelectTimeEnum.HALF_DAY: {
              formState.selectTime = time.selectTime;
              formState.timeFrame = time.timeFrame;
              formState.halfDay = dayjs(update.data?.requestFrom);
              onChangeHalfDay(update.data?.requestFrom);
              break;
            }
            case SelectTimeEnum.CUSTOM_DAY: {
              formState.selectTime = time.selectTime;
              formState.fromDate = dayjs(update.data?.requestFrom);
              formState.fromTime = dayjs(update.data?.requestFrom);
              formState.toDate = dayjs(update.data?.requestTo);
              formState.toTime = dayjs(update.data?.requestTo);
              break;
            }
          }
        }
        if (update.data?.requestTypeCode) {
          const findRequestType = requestTypes.value.find(
            (item) => item.requestTypeCode === update.data?.requestTypeCode
          );
          formState.requestType = {
            value: findRequestType?.requestTypeCode ?? '',
            label: findRequestType?.requestTypeName ?? '',
            option: {
              requiredLevel1Approval:
                findRequestType?.requiredLevel1Approval ?? false,
              requiredLevel2Approval:
                findRequestType?.requiredLevel2Approval ?? false,
            },
          };
        }
        if (update.data?.leaveTypeCode) {
          formState.leaveType = {
            value: update.data?.leaveTypeCode ?? '',
            label: update.data?.leaveTypeName ?? '',
          };
        }
        if (update.data?.projectId) {
          formState.project = {
            value: update.data?.projectId ?? '',
            label: update.data?.projectName ?? '',
          };
        }
        formState.description = update.data?.description ?? '';
      } catch (error) {
        logger.error(error);
      } finally {
        modalLoading.value = false;
      }

      break;
    }
    default:
      break;
  }
}

async function handleDeleteRequest(id: string) {
  try {
    const del = await deleteRequest(id);
    if (del.status !== ResponseStatusEnum.SUCCESS) return;

    message.success(del.message);
  } catch (error) {
    logger.error(error);
  } finally {
    refresh();
  }
}

function handleTableChange(
  pagination: TablePaginationConfig,
  filters: Record<string, FilterValue>
) {
  searchForm.value.pageSize = pagination.pageSize;
  searchForm.value.pageNum = pagination.current;
  run({ ...searchForm.value, ...filters });
}

function handlePaginationChange(page: number, pageSize: number) {
  searchForm.value.pageSize = pageSize;
  searchForm.value.pageNum = page;
  run(searchForm.value);
}

function onReset() {
  Object.assign(formState, cloneDeep(initFormState));
}

const renderTitle = computed(() => {
  switch (modalType.value) {
    case ModalType.ADD:
    case ModalType.COPY:
      return _.startCase(_.toLower(`${t('button.add')} ${t('form.request')}`));
    case ModalType.EDIT:
      return `${t('button.edit')} ${t('form.request')}`;
    default:
      return '';
  }
});

const renderOkConfirm = computed(() => {
  switch (modalType.value) {
    case ModalType.ADD:
    case ModalType.COPY:
      return t('message.add-confirmation');
    case ModalType.EDIT:
      return t('message.edit-confirmation');
    default:
      return '';
  }
});

function onSearch() {
  searchForm.value.fromDate = dayjs(searchDate.value)
    .startOf('month')
    .format('YYYY-MM-DD');
  searchForm.value.toDate = dayjs(searchDate.value)
    .endOf('month')
    .format('YYYY-MM-DD');
  handleTableChange(
    { pageSize: searchForm.value.pageSize ?? 10, current: 1 },
    {}
  );
}

const calculateDate = computed(() => {
  return (requestFrom: string, requestTo: string) => {
    if (!requestFrom || !requestTo) return { days: 0, hours: 0, minutes: 0 };

    const days = dayjs(requestTo).diff(dayjs(requestFrom), 'day');
    const hours = dayjs(requestTo).diff(dayjs(requestFrom), 'hour');
    const minutes = dayjs(requestTo).diff(dayjs(requestFrom), 'minute');

    return {
      days,
      hours: hours - days * 24,
      minutes: minutes - hours * 60,
    };
  };
});

const calculateDateFormState = computed(() => {
  const fromDate =
    formState.fromDate && dayjs(formState.fromDate).format('YYYY-MM-DD');
  const fromTime =
    formState.fromTime && dayjs(formState.fromTime).format('HH:mm');
  const toDate =
    formState.toDate && dayjs(formState.toDate).format('YYYY-MM-DD');
  const toTime = formState.toTime && dayjs(formState.toTime).format('HH:mm');
  const from = fromDate ? `${fromDate}${fromTime ? ` ${fromTime}` : ''}` : '';
  const to = toDate ? `${toDate}${toTime ? ` ${toTime}` : ''}` : '';
  return calculateDate.value(from, to);
});

function onRequestTypeChange() {
  formState.fromDate = undefined;
  formState.fromTime = undefined;
  formState.toDate = undefined;
  formState.toTime = undefined;
  formState.project = undefined;
}

function disabledDate(current: dayjs.Dayjs) {
  if (!current) return false;
  if (formState.requestType?.value === RequestTypeEnum.OVERTIME) {
    if (current > dayjs(formState.fromDate).add(1, 'day')) return true;
    if (current < dayjs(formState.fromDate)) return true;
    return false;
  }

  return false;
}

onMounted(async () => {
  const res = await Promise.all([
    getRequestTypeList(),
    getLeaveTypeList(),
    getEmployeeApprovalAuthority(),
  ])
  requestTypes.value = res[0].data?.items ?? []
  leaveTypes.value = res[1].data?.items ?? []
  employees.value = res[2].data?.items ?? []
})

const showOnSiteApprover = computed(() => {
  const { IN_OUT, LEAVE, OVERTIME, WORK } = RequestTypeEnum;
  if (
    [OVERTIME, IN_OUT].includes(formState.requestType?.value as RequestTypeEnum)
  )
    return false;

  if ([LEAVE, WORK].includes(formState.requestType?.value as RequestTypeEnum)) {
    if (formState.project?.value) return true;
    return false;
  }
  return false;
});

const onSiteApprover = computed(() => {
  return projects
    .filter(item => item.id === formState.project?.value)
    .map(item => item.projectManagers.map(i => i.employeeName.trim()))
    .flat()
    .join(', ');
});

const showOfficeApprover = computed(() => {
  const { LEAVE, WORK } = RequestTypeEnum;
  if ([LEAVE, WORK].includes(formState.requestType?.value as RequestTypeEnum))
    return true;

  return false;
});

const officeApprover = computed(() => {
  return projects
    .filter(item => item.isOffice === true)
    .map(item => item.projectManagers.map(i => i.employeeName.trim()))
    .flat()
    .join(', ');
});

const representativeApprover = computed(() => {
  return employees.value.map((item) => item.employeeName.trim()).join(', ');
});

const projectFilter = computed(() => {
  const { requiredLevel1Approval, requiredLevel2Approval }
    = formState.requestType?.option ?? {}
  return projects.filter((item: any) => {
    if (requiredLevel1Approval && requiredLevel2Approval)
      return item.isOffice === false;
    if (requiredLevel1Approval) return item.isOffice === false;
    if (requiredLevel2Approval) return item.isOffice === true;
    return true;
  });
});

watch(
  () => t('locale'),
  () => onSearch()
);

function getRequestTypeDescription(requestType?: string) {
  const description = RequestType[requestType as RequestTypeEnum]?.description;
  return description ? t(description) : '';
}
</script>

<template>
  <page-container>
    <a-row
      :wrap="false"
      :gutter="[12, 12]"
      class="h-[calc(100vh-100px)] flex-col"
    >
      <a-col flex="none" span="24">
        <a-row :gutter="[12, 12]">
          <a-col span="24">
            <a-row :gutter="[12, 12]" align="middle">
              <a-col flex="none">
                <a-row :gutter="[24, 24]" align="middle">
                  <a-col>
                    <div class="font-bold text-base">
                      {{ t('form.your-request') }}
                    </div>
                  </a-col>
                  <a-col>
                    <a-button
                      class="flex flex-items-center"
                      type="primary"
                      @click="openModal('', ModalType.ADD)"
                    >
                      <PlusOutlined />
                      {{ `${t('button.new')} ${t('form.request')}` }}
                    </a-button>
                  </a-col>
                </a-row>
              </a-col>
              <a-col flex="auto">
                <div class="flex gap-10 justify-end">
                  <div class="flex items-center">
                    <LeftOutlined
                      class="flex justify-center w-6 h-6 bg-white rounded-full"
                      @click="
                        searchDate = dayjs(searchDate).subtract(1, 'month');
                        onSearch();
                      "
                    />
                    <a-date-picker
                      v-model:value="searchDate"
                      picker="month"
                      :allow-clear="false"
                      :format="(value: dayjs.Dayjs) =>
                        `${value
                          .startOf('month')
                          .format('YYYY/MM/DD')} - ${value
                          .endOf('month')
                          .format('YYYY/MM/DD')}`
                      "
                      class="search-date"
                      @change="onSearch"
                    />
                    <RightOutlined
                      class="flex justify-center w-6 h-6 bg-white rounded-full"
                      @click="
                        searchDate = dayjs(searchDate).add(1, 'month');
                        onSearch();
                      "
                    />
                  </div>
                  <a-checkbox-group
                    v-model:value="searchForm.statusCode"
                    @change="onSearch"
                  >
                    <a-row :gutter="[12, 12]">
                      <a-col v-for="item in RequestStatus" :key="item.value">
                        <a-checkbox :value="item.value">
                          {{ t(item.value) }}
                        </a-checkbox>
                      </a-col>
                    </a-row>
                  </a-checkbox-group>
                </div>
              </a-col>
            </a-row>
          </a-col>
          <a-col span="24">
            <a-table
              class="tableRequest"
              :scroll="{ x: 'max-content', y: 'calc(100vh - 205px)' }"
              :columns="columns"
              :data-source="dataSource?.items"
              :loading="loading"
              :pagination="false"
              row-key="requestId"
              :show-header="false"
              @change="handleTableChange"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'status'">
                  <a-tag
                    class="min-w-full h-[40px] flex justify-center items-center text-sm font-bold"
                    :color="
                      RequestStatus[record.statusCode as SystemStatusEnumKey]
                        ?.color
                    "
                  >
                    {{ record.statusName }}
                  </a-tag>
                </template>
                <template v-if="column.dataIndex === 'request'">
                  <a-row :gutter="[12, 12]">
                    <a-col :span="24">
                      <a-row :gutter="[12, 12]" :wrap="false">
                        <a-col span="12">
                          <a-row :gutter="[12, 12]" :wrap="false">
                            <a-col :span="getSpanLeft.name">
                              <a-typography-text :level="5" class="font-bold">
                                {{ record.requestTypeName }}
                              </a-typography-text>
                            </a-col>
                            <a-col :span="getSpanLeft.type">
                              <div
                                v-if="record.leaveTypeName"
                                class="flex flex-wrap gap-2"
                              >
                                <span class="text-gray-500">
                                  {{ t('form.leave-type') }}:
                                </span>
                                <span class="font-medium">
                                  {{ record.leaveTypeName }}
                                </span>
                              </div>
                            </a-col>
                            <a-col :span="getSpanLeft.date">
                              <div class="flex flex-wrap gap-2">
                                <div class="flex items-center gap-2">
                                  <img src="/icon/calendar_project_icon.svg" />
                                  <span class="text-gray-500">
                                    {{
                                      calculateDate(
                                        record.requestFrom,
                                        record.requestTo
                                      ).days
                                    }}
                                    {{ t('form.days').toLowerCase() }}
                                    {{
                                      calculateDate(
                                        record.requestFrom,
                                        record.requestTo
                                      ).hours
                                    }}
                                    {{ t('form.hours').toLowerCase() }}
                                    {{
                                      calculateDate(
                                        record.requestFrom,
                                        record.requestTo
                                      ).minutes
                                    }}
                                    {{ t('form.minutes').toLowerCase() }}
                                  </span>
                                </div>
                                <span class="font-medium">
                                  {{
                                    dayjs(record.requestFrom).format(
                                      'YYYY.MM.DD'
                                    )
                                  }}
                                  <ArrowRightOutlined />
                                  {{
                                    dayjs(record.requestTo).format('YYYY.MM.DD')
                                  }}
                                </span>
                              </div>
                            </a-col>
                          </a-row>
                        </a-col>
                        <a-col span="12">
                          <a-row :gutter="[12, 12]" :wrap="false">
                            <a-col :span="getSpanRight.author">
                              <div class="flex flex-wrap gap-2">
                                <div class="flex items-center gap-2">
                                  <img src="/icon/user.svg" />
                                  <span class="text-gray-500">
                                    {{ t('form.author') }}:
                                  </span>
                                </div>
                                <tag>
                                  {{ record.createUserName }}
                                </tag>
                              </div>
                            </a-col>
                            <a-col :span="getSpanRight.approver">
                              <div class="flex flex-wrap gap-2">
                                <div class="flex items-center gap-2">
                                  <img src="/icon/user.svg" />
                                  <span
                                    v-if="
                                      record.status ===
                                      RequestStatusEnum.REJECTED
                                    "
                                    class="text-gray-500"
                                  >
                                    {{ t('form.rejected-by') }}:
                                  </span>
                                  <span v-else class="text-gray-500">
                                    {{ t('form.approved-by') }}:
                                  </span>
                                </div>
                                <tag
                                  v-if="record.approver1Name"
                                  :color="
                                    RequestStatus[
                                      record.statusCode as SystemStatusEnumKey
                                    ]?.color
                                  "
                                >
                                  {{ record.approver1Name }}
                                  ({{
                                    dayjs(record.approver1Time).format(
                                      'YYYY.MM.DD HH:mm'
                                    )
                                  }})
                                </tag>
                                <tag
                                  v-if="record.approver2Name"
                                  :color="
                                    RequestStatus[
                                      record.statusCode as SystemStatusEnumKey
                                    ]?.color
                                  "
                                >
                                  {{ record.approver2Name }}
                                  ({{
                                    dayjs(record.approver2Time).format(
                                      'YYYY.MM.DD HH:mm'
                                    )
                                  }})
                                </tag>
                                <tag
                                  v-if="
                                    !record.approver1Name &&
                                    !record.approver2Name
                                  "
                                  color="volcano"
                                >
                                  {{ t('NOT_APPROVED') }}
                                </tag>
                              </div>
                            </a-col>
                          </a-row>
                        </a-col>
                      </a-row>
                    </a-col>
                    <a-col :span="24">
                      <div>
                        <span class="text-gray-500">
                          {{ t('form.reason') }}:
                        </span>
                        <span class="font-medium">
                          {{ record.description }}
                        </span>
                      </div>
                    </a-col>
                  </a-row>
                </template>
                <template v-if="column.dataIndex === 'createTime'">
                  <div class="flex items-center gap-2">
                    <img src="/icon/clock.svg" />
                    <span class="text-gray-500">
                      {{ dayjs().diff(dayjs(record.createTime), 'day') }}
                      {{ t('form.days').toLowerCase() }}
                    </span>
                  </div>
                </template>
                <template v-if="column.dataIndex === 'action'">
                  <div class="flex flex-justify-center gap-2">
                    <a-button
                      class="flex items-center"
                      ghost
                      size="small"
                      @click="openModal(record.requestId, ModalType.EDIT)"
                    >
                      <img src="/icon/edit.svg" class="w-[20px]" />
                    </a-button>
                    <a-popconfirm
                      :title="t('message.delete-confirmation')"
                      @confirm="() => handleDeleteRequest(record.requestId)"
                    >
                      <a-button class="flex items-center" size="small" ghost>
                        <img src="/icon/delete.svg" class="w-[20px]" />
                      </a-button>
                    </a-popconfirm>
                  </div>
                </template>
              </template>
            </a-table>
          </a-col>
        </a-row>
      </a-col>

      <a-col flex="auto" span="24">
        <div class="h-full flex items-end">
          <a-row justify="space-between" class="mt-4 w-full">
            <a-col>
              <a-pagination
                class="pagination"
                :total="pagination.total"
                :current="pagination.current"
                :page-size="pagination.pageSize"
                @change="handlePaginationChange"
              />
            </a-col>
            <a-col>
              <a-row :gutter="[12, 12]" justify="center" align="middle">
                <a-col>{{ t('show') }}</a-col>
                <a-col>
                  <a-pagination
                    class="pagination pagination-right"
                    :total="pagination.total"
                    :current="pagination.current"
                    :page-size="pagination.pageSize"
                    show-size-changer
                    :build-option-text="(props: any) => props.value"
                    @change="handlePaginationChange"
                  />
                </a-col>
                <a-col>{{ t('entries') }}</a-col>
              </a-row>
            </a-col>
          </a-row>
        </div>
      </a-col>
    </a-row>

    <a-modal
      v-model:open="isOpenModal"
      width="800px"
      :footer="false"
      :closable="false"
      :mask-closable="false"
      @cancel="onReset"
    >
      <template #title>
        <div class="flex justify-center items-center">
          <a-typography-title :level="4" class="!text-[#256CB5]">
            {{ renderTitle }}
          </a-typography-title>
        </div>
      </template>
      <a-card border-style="none" :loading="modalLoading" class="card">
        <a-form
          ref="formRef"
          :model="formState"
          :label-col="{ span: 24 }"
          :wrapper-col="{ span: 24 }"
          autocomplete="off"
          @finish="onFinish"
        >
          <a-row :gutter="[12, 12]">
            <a-col span="12">
              <a-form-item
                :label="t('form.request-type')"
                name="requestType"
                :rules="[{ required: true }]"
              >
                <a-select
                  v-model:value="formState.requestType"
                  :placeholder="t('form.request-type')"
                  allow-clear
                  label-in-value
                  @change="onRequestTypeChange"
                >
                  <a-select-option
                    v-for="item in requestTypes"
                    :key="item.requestTypeCode"
                    :value="item.requestTypeCode"
                    :required-level1-approval="item.requiredLevel1Approval"
                    :required-level2-approval="item.requiredLevel2Approval"
                  >
                    {{ item.requestTypeName }}
                  </a-select-option>
                </a-select>
                <div class="mt-2 text-xs font-normal text-gray-500">
                  {{ getRequestTypeDescription(formState.requestType?.value) }}
                </div>
              </a-form-item>
            </a-col>
            <a-col
              v-if="formState.requestType?.value === RequestTypeEnum.LEAVE"
              span="12"
            >
              <a-form-item
                :label="t('form.leave-type')"
                name="leaveType"
                :rules="[{ required: true }]"
              >
                <a-select
                  v-model:value="formState.leaveType"
                  :placeholder="t('form.leave-type')"
                  allow-clear
                  label-in-value
                >
                  <a-select-option
                    v-for="item in leaveTypes"
                    :key="item.leaveTypeCode"
                    :value="item.leaveTypeCode"
                  >
                    {{ item.leaveTypeName }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item :label="t('form.project')" name="project">
                <a-select
                  v-model:value="formState.project"
                  :placeholder="t('form.project')"
                  allow-clear
                  label-in-value
                >
                  <a-select-option
                    v-for="item in projectFilter"
                    :key="item.id"
                    :value="item.id"
                  >
                    {{ item.name }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item
                :label="t('placeholder.select-time')"
                name="selectTime"
                :rules="[{ required: true }]"
              >
                <a-select
                  v-model:value="formState.selectTime"
                  :options="selectTimeOptions"
                  @change="onChangeSelectTime"
                />
              </a-form-item>
            </a-col>
            <a-col v-if="formState.selectTime === SelectTimeEnum.DAY" span="12">
              <a-form-item
                :label="t('day')"
                name="day"
                :rules="[{ required: true }]"
              >
                <a-date-picker
                  v-model:value="formState.day"
                  class="!w-full"
                  format="YYYY-MM-DD"
                  picker="date"
                  @change="onChangeDay"
                />
              </a-form-item>
            </a-col>
            <a-col
              v-if="formState.selectTime === SelectTimeEnum.HALF_DAY"
              span="12"
            >
              <a-form-item
                :label="t('half-day')"
                name="halfDay"
                :rules="[{ required: true }]"
              >
                <a-input-group compact>
                  <a-date-picker
                    v-model:value="formState.halfDay"
                    class="!w-1/2"
                    format="YYYY-MM-DD"
                    picker="date"
                    @change="onChangeHalfDay"
                  />
                  <a-select
                    v-model:value="formState.timeFrame"
                    class="!w-1/2"
                    :options="timeFrameOptions"
                    @change="onChangeTimeFrame"
                  />
                </a-input-group>
              </a-form-item>
            </a-col>
            <a-col
              v-if="formState.selectTime === SelectTimeEnum.CUSTOM_DAY"
              span="12"
            >
              <a-form-item
                :label="t('form.start-date')"
                name="fromDate"
                :rules="[{ required: true }]"
              >
                <a-input-group compact>
                  <a-date-picker
                    v-model:value="formState.fromDate"
                    class="!w-1/2"
                    format="YYYY-MM-DD"
                    picker="date"
                  />
                  <TimePicker
                    v-model:value="formState.fromTime"
                    class="w-1/2"
                    :minute-step="5"
                    value-type="dayjs"
                  />
                </a-input-group>
              </a-form-item>
            </a-col>
            <a-col
              v-if="formState.selectTime === SelectTimeEnum.CUSTOM_DAY"
              span="12"
            >
              <a-form-item
                :label="t('form.end-date')"
                name="toDate"
                :rules="[{ required: true }]"
              >
                <a-input-group compact>
                  <a-date-picker
                    v-model:value="formState.toDate"
                    class="!w-1/2"
                    :disabled-date="disabledDate"
                    format="YYYY-MM-DD"
                  />
                  <TimePicker
                    v-model:value="formState.toTime"
                    class="w-1/2"
                    :minute-step="5"
                    value-type="dayjs"
                  />
                </a-input-group>
              </a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item :label="t('form.request-duration')">
                <a-input-group compact>
                  <a-input
                    :value="`${calculateDateFormState.days} ${t('form.days')}`"
                    disabled
                    class="!w-1/3"
                  />
                  <a-input
                    :value="`${calculateDateFormState.hours} ${t(
                      'form.hours'
                    )}`"
                    disabled
                    class="!w-1/3"
                  />
                  <a-input
                    :value="`${calculateDateFormState.minutes} ${t(
                      'form.minutes'
                    )}`"
                    disabled
                    class="!w-1/3"
                  />
                </a-input-group>
              </a-form-item>
            </a-col>
            <a-col span="24">
              <a-form-item :label="t('form.reason')" name="description">
                <a-textarea
                  v-model:value="formState.description"
                  :placeholder="t('form.reason')"
                  :rows="4"
                />
              </a-form-item>
            </a-col>
            <a-col v-if="showOnSiteApprover" span="24">
              <a-form-item :label="t('on-site-approver')">
                <div class="font-semibold">
                  {{ onSiteApprover }}
                </div>
              </a-form-item>
            </a-col>
            <a-col v-if="showOfficeApprover" span="24">
              <a-form-item :label="t('office-approver')">
                <div class="font-semibold">
                  {{ officeApprover }}
                </div>
              </a-form-item>
            </a-col>
            <a-col span="24">
              <a-form-item>
                <template #label>
                  <div class="flex justify-center items-center gap-3">
                    <span>{{ t('representative-approver') }}</span>
                    <RightOutlined
                      v-if="!isRepresentativeApprover"
                      class="cursor-pointer text-blue"
                      @click="isRepresentativeApprover = true"
                    />
                    <DownOutlined
                      v-else
                      class="cursor-pointer text-blue"
                      @click="isRepresentativeApprover = false"
                    />
                  </div>
                </template>
                <div v-if="isRepresentativeApprover" class="font-semibold">
                  {{ representativeApprover }}
                </div>
              </a-form-item>
            </a-col>
          </a-row>

          <a-row justify="end">
            <a-row :gutter="[4, 4]">
              <a-col>
                <a-button
                  @click="
                    () => {
                      isOpenModal = false;
                      onReset();
                    }
                  "
                >
                  {{ t('button.cancel') }}
                </a-button>
              </a-col>
              <a-col>
                <a-popconfirm :title="renderOkConfirm" @confirm="onFinish">
                  <a-button type="primary">
                    {{ t('button.save') }}
                  </a-button>
                </a-popconfirm>
              </a-col>
            </a-row>
          </a-row>
        </a-form>
      </a-card>
    </a-modal>
  </page-container>
</template>

<style lang="less" scoped>
.tableRequest {
  :deep(.ant-table) {
    background: transparent;
  }
  :deep(table) {
    border-collapse: separate;
    border-spacing: 0 24px;
    margin-top: -24px;
    margin-right: 10px;
  }
  :deep(.ant-table-container) {
    padding-top: 24px;
  }
  :deep(.ant-table-tbody > tr) {
    background: #fff;
    box-shadow: 0px 2px 4px 0px #0000001a;
    border-radius: 8px;
  }
  :deep(.ant-table-tbody > tr > td:first-child) {
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
  }
  :deep(.ant-table-tbody > tr > td:last-child) {
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
  }
  :deep(.ant-table-tbody > tr > td) {
    background: #fff !important;
    transition: none;
  }
}
.pagination {
  :deep(.ant-pagination-item-active) {
    background: #f99649;
    border-color: #f99649;
    a {
      color: #fff;
    }
  }
  :deep(.ant-select-selector) {
    background: #f99649;
    border-color: #f99649 !important;
    .ant-select-selection-item {
      color: #fff;
    }
  }
  :deep(.ant-select-arrow) {
    color: #fff;
  }
}
.pagination-right {
  :deep(.ant-pagination-prev) {
    display: none;
  }
  :deep(.ant-pagination-next) {
    display: none;
  }
  :deep(.ant-pagination-item) {
    display: none;
  }
  :deep(.ant-pagination-options) {
    margin: 0;
  }
  :deep(.ant-pagination-jump-next) {
    display: none;
  }
  :deep(.ant-pagination-jump-prev) {
    display: none;
  }
}
.search-date {
  border: none;
  background: none;
  box-shadow: none;

  :deep(.ant-picker-suffix) {
    display: none;
  }
  :deep(input) {
    cursor: pointer;
    width: 165px;
  }
}
</style>
