<!-- eslint-disable curly -->
<!-- eslint-disable antfu/top-level-function -->
<script lang="ts" setup>
// import axios from 'axios'
import { Modal, type TableProps } from 'ant-design-vue'

import dayjs from 'dayjs'
import type { SortOrder } from 'ant-design-vue/es/table/interface'
import {
  CheckOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons-vue'
import { type CSSProperties, createVNode } from 'vue'
import { createAttendanceApi, deleteAttendanceApi, updateAttendanceApi } from '~@/api/attendance'

import type { AttendanceItem, AttendanceItemParams, AttendanceUpdateParams, BreakTimeItem } from '~@/api/attendance'
import logger from '~@/utils/logger'
import WorkTimeModal from '~@/components/common/WorkTimeModal.vue'

const props = defineProps({
  dataSource: {
    type: Array as () => AttendanceItem[],
    required: true,
  },
  selectedDate: {
    type: dayjs.Dayjs,
  },
  scroll: {
    type: Object as () => {
      x?: string | number | true
      y?: string | number
    },
    default: () => ({
      x: '30vw',
      y: '100%',
    }),
  },
  typeOfSort: {
    type: String,
    default: 'descend',
  },
  fontSize: {
    type: Number,
    default: 14,
  },
  rowHeight: {
    type: Number,
    default: 40,
  },
})

const emit = defineEmits<{
  // (event: 'fastCheckIn', shiftId: string, params: FastCheckInParams): void
  (event: 'attendanceDailyRequest', employeeShiftId: string): void
  (event: 'refreshAttendance'): void
  (event: 'update:selectedDate', date: dayjs.Dayjs): void
}>()

const { t } = useI18n()
const messageNotify = useMessage()
const idDetail = ref<string>('')
const loading = shallowRef(false)
const isWorkTimeModalOpened = ref(false)
const attendanceItem = ref<AttendanceItem | null>(null)
const sortedData = ref<AttendanceItem[]>([])
const sortedInfo = ref({
  columnKey: 'workingDate',
  order: props.typeOfSort,
})

// Font size and row height controls
const fontSize = ref(props.fontSize) // Default smaller font size
const rowHeight = ref(props.rowHeight) // Default smaller row height

const columns = computed(() => {
  const sorted = sortedInfo.value || {}
  return [
    {
      title: t('dashboard.workplace.timeKeepingData.date'),
      dataIndex: 'workingDate',
      fixed: 'left',
      key: 'workingDate',
      align: 'center',
      sortOrder: sorted.columnKey === 'workingDate' ? sorted.order as SortOrder : undefined,
      customCell: (data: AttendanceItem, index: number = 0) => {
        if (sortedData.value.length > 0) {
          if (index === 0 || sortedData.value[index - 1].workingDate !== data.workingDate) {
            let count = 1
            for (let i = index + 1; i < sortedData.value.length; i++) {
              if (sortedData.value[i].workingDate === data.workingDate) {
                count += 1
              }
            }
            return { rowSpan: count }
          }
          else {
            return { rowSpan: 0 }
          }
        }
        else {
          return { rowSpan: 1 }
        }
      },
    },
    {
      title: t('project'),
      dataIndex: 'projectName',
      responsive: ['lg'],
      key: 'projectName',
      align: 'center',
      ellipsis: true,
    },
    {
      title: t('table.checkIn'),
      dataIndex: 'checkInTime',
      align: 'center',
    },
    {
      title: t('table.breakTime'),
      dataIndex: 'breakList',
      key: 'breakList',
      align: 'center',
    },
    {
      title: t('table.checkOut'),
      dataIndex: 'checkOutTime',
      align: 'center',
    },
    {
      title: t('totalWorkTime'),
      dataIndex: 'totalWorkTime',
      align: 'center',
      customRender: ({ record }: { record: AttendanceItem }) => {
        if (!record.totalWorkTime)
          return '-'
        return Math.round(record.totalWorkTime * 100) / 100
      },
    },
    {
      title: t('totalOvertime'),
      dataIndex: 'totalOverTime',
      align: 'center',
      customRender: ({ record }: { record: AttendanceItem }) => {
        if (!record.totalOverTime)
          return '-'
        return Math.round(record.totalOverTime * 100) / 100
      },
    },
    {
      title: t('approvalStatus'),
      dataIndex: 'isRequested',
      align: 'center',
    },
    {
      title: t('table.comment'),
      dataIndex: 'description',
      align: 'center',
      ellipsis: true,
      customRender: ({ record }: { record: AttendanceItem }) => {
        if (!record.description)
          return '-'
        return record.description
      },
    },
    {
      title: t('action'),
      dataIndex: 'action',
      align: 'center',
      fixed: 'right',
    },
  ]
})

const calcWidth = computed(() => {
  return rowHeight.value - 12
})
const calcHeight = computed(() => {
  return rowHeight.value - 12
})

const sortDataByType = (type: string) => {
  let sortValue = -1
  if (type === 'descend') {
    sortValue = 1
  }
  sortedData.value = sortedData.value.sort((a: AttendanceItem, b: AttendanceItem) => {
    const dateA = dayjs(a.workingDate)
    const dateB = dayjs(b.workingDate)
    if (dateA.isBefore(dateB))
      return sortValue
    if (dateA.isAfter(dateB))
      return sortValue * -1
    return 0
  })
}

const handleChange: TableProps['onChange'] = (_, __, sorter) => {
  sortedInfo.value = sorter as { columnKey: string; order: string }
  sortDataByType(sortedInfo.value.order)
}

const customizeTable = computed(() => {
  const style: CSSProperties = {
    fontSize: `${fontSize.value}px`,
    '--row-height': `${rowHeight.value}px`,
    '--font-size': `${fontSize.value}px`,
  }
  return style
})

// const fastCheckIn = async (employeeShift: AttendanceItem) => {
//   const shiftId = employeeShift.employeeShiftId
//   if (!shiftId) {
//     return
//   }
//   const params: FastCheckInParams = {
//     latitude: '',
//     longitude: '',
//   }
//   emit('fastCheckIn', shiftId, params)
// }

// const isFastCheckInSatify = (employeeShift: AttendanceItem) => {
//   if (!employeeShift.checkInTime && employeeShift.scheduledStartTime && employeeShift.scheduledEndTime) {
//     return true
//   }
//   return false
// }
function getModifierColor(modifier?: 'SYSTEM' | 'AUTHOR' | 'MANAGER') {
  if (!modifier)
    return 'text-blue-600'
  const colors: Record<string, string> = {
    SYSTEM: 'text-blue-600',
    AUTHOR: 'text-orange',
    MANAGER: 'text-green-600',
    AUTO: 'text-red-600',
  }
  return colors[modifier] || 'text-blue-600'
}

function viewAttendanceDetail(employeeShift: AttendanceItem) {
  isWorkTimeModalOpened.value = true
  attendanceItem.value = employeeShift
}

async function deleteAttendance(employeeShiftId: string) {
  if (loading.value)
    return
  try {
    loading.value = true
    const { status, message } = await deleteAttendanceApi(employeeShiftId)
    if (status === 200) {
      messageNotify.success(message)
      emit('refreshAttendance')
    }
    else {
      messageNotify.error(message)
    }
  }
  catch (e) {
    logger.error(e)
  }
  finally {
    loading.value = false
  }
}

function handleDeleteShift(employeeShift: AttendanceItem) {
  Modal.confirm({
    title: t('confirm.delete'),
    icon: createVNode(ExclamationCircleOutlined),
    content: createVNode('div', {}, `${t('alert.confirmDelete', { msg: t('attendance') })}`),
    cancelText: t('button.cancel'),
    okText: t('button.ok'),
    async onOk() {
      if (!employeeShift?.employeeShiftId)
        return
      await deleteAttendance(employeeShift.employeeShiftId)
      emit('refreshAttendance')
    },
    onCancel() { },
  })
}

const onUpdateAttendance = async (employeeShiftId: string, params: AttendanceUpdateParams) => {
  if (loading.value)
    return
  loading.value = true
  try {
    const { status, message } = await updateAttendanceApi(employeeShiftId, params)
    if (status === 200) {
      messageNotify.success(message)
    }
  }
  catch (e) {
  }
  finally {
    loading.value = false
  }
}

const onCreatNewAttendance = async (params: AttendanceUpdateParams) => {
  if (loading.value)
    return
  loading.value = true
  try {
    const { status, message } = await createAttendanceApi(params)
    if (status === 200) {
      messageNotify.success(message)
      emit('refreshAttendance')
    }
    else {
      messageNotify.error(message)
    }
  }
  catch (e) {
  }
  finally {
    loading.value = false
  }
}

async function onSaveAttendance(employeeShiftId: string, params: AttendanceItemParams) {
  if (employeeShiftId !== '') {
    await onUpdateAttendance(employeeShiftId, params)
    emit('refreshAttendance')
  }
  else {
    await onCreatNewAttendance(params)
    emit('refreshAttendance')
  }
}

const getRowSpan = (workingDate?: string) => {
  if (!workingDate || sortedData.value.length === 0)
    return 1

  let count = 0
  for (let i = 0; i < sortedData.value.length; i++) {
    if (sortedData.value[i].workingDate === workingDate) {
      count += 1
    }
  }
  return count
}

const customRow = computed(() => (data: AttendanceItemParams) => {
  const isActive = data.workingDate === idDetail.value
  let classActive = ''
  if (isActive)
    classActive += 'row-active'
  if (isActive && getRowSpan(data.workingDate) === 1)
    classActive += ' row-alone'

  return {
    onClick: () => {
      idDetail.value = data.workingDate ?? ''
      emit('update:selectedDate', dayjs(data.workingDate))
    },
    class: classActive,
  }
})

const showBreakTime = (timeValue?: string) => {
  if (!timeValue)
    return '--:--'
  return timeValue.slice(0, 5)
}

watch(() => props.dataSource, (newVal) => {
  sortedData.value = [
    ...newVal,
  ]
}, { deep: true })

watch(() => props.fontSize, (newVal) => {
  fontSize.value = newVal
})

watch(() => props.rowHeight, (newVal) => {
  rowHeight.value = newVal
})

onMounted(async () => {
  sortedData.value = props.dataSource
})
</script>

<template>
  <a-table class="tableAttendance" :loading="loading" :columns="columns as any" :data-source="dataSource" bordered
    :pagination="false" :style="customizeTable" :scroll="{ x: '70%', y: '100%' }" row-key="workingDate"
    :custom-row="customRow" @change="handleChange">
    <template #bodyCell="{ column, record }">
      <template v-if="column.dataIndex === 'projectName'">
        <a-tooltip :title="record.projectName">
          <span class="overflow-hidden text-ellipsis whitespace-nowrap">
            {{ record.projectName }}
          </span>
        </a-tooltip>
      </template>

      <template v-if="column.dataIndex === 'checkInTime'">
        <span v-if="record.checkInTime" :class="getModifierColor(record?.modifiedCheckInTimeLastModifierType)">
          {{ record.checkInTime.slice(0, 5) }}
        </span>
        <span v-else-if="record.scheduledStartTime" class="text-gray-400">
          {{ record.scheduledStartTime.slice(0, 5) }}
        </span>
      </template>
      <template v-if="column.dataIndex === 'checkOutTime'">
        <span v-if="record.checkOutTime" :class="getModifierColor(record?.modifiedCheckOutTimeLastModifierType)">
          {{ record.checkOutTime.slice(0, 5) }}
        </span>
        <span v-else-if="record.scheduledEndTime" class="text-gray-400">
          {{ record.scheduledEndTime.slice(0, 5) }}
        </span>
      </template>
      <template v-if="column.dataIndex === 'isRequested'">
        <template v-if="record.isRequested">
          <template v-if="record.isApproved === true">
            <a-tag class="text-[1rem] text-[#278836] bg-[#C7ECD1] rounded-[1rem] border-[#278836] p-1">
              <CheckOutlined />
              {{ t('dashboard.workplace.timeKeepingData.approval') }}
            </a-tag>
          </template>
          <template v-else-if="record.isApproved === null">
            <div class="flex justify-center">
              <a-tag
                class="flex justify-center items-center w-[7rem] text-[1rem] text-[#DC6000] bg-[#FCE9D2] rounded-[1rem] border-[#DC6000] p-1">
                <!-- <a-avatar src="/icon/pending_icon.svg" shape="square" class="w-[1rem] h-[1rem] mr-[3px]" /> -->
                <span>
                  {{ t('dashboard.workplace.timeKeepingData.pending') }}
                </span>
              </a-tag>
            </div>
          </template>
        </template>
      </template>
      <template v-if="column.dataIndex === 'workingDate'">
        <span v-if="record.workingDate" color="#08223B">
          {{ dayjs(record.workingDate, 'YYYY-MM-DD').format("MM/DD (dd)") }}
        </span>
      </template>
      <template v-if="column.dataIndex === 'breakList'">
        <template v-if="record.modifiedBreakList">
          <a-tooltip
            :title="record.modifiedBreakList.map((item: BreakTimeItem) => `${showBreakTime(item.breakInTime)} ~ ${showBreakTime(item.breakOutTime)}`).join(' / ')">
            <div class="overflow-hidden text-ellipsis whitespace-nowrap"
              :class="getModifierColor(record?.modifiedBreakListLastModifierType)">
              <template v-for="(item, index) in record.modifiedBreakList" :key="item">
                <span>
                  {{ showBreakTime(item.breakInTime) }} ~ {{ showBreakTime(item.breakOutTime) }}
                </span>
                <span v-if="index !== record.modifiedBreakList.length - 1">
                  &nbsp;/&nbsp;
                </span>
              </template>
            </div>
          </a-tooltip>
          <<<<<<< HEAD=======>>>>>>> test
        </template>
        <template v-else-if="!record.breakList?.length">
          <span>-</span>
        </template>
        <template v-else>
          <a-tooltip
            :title="record.breakList.map((item: BreakTimeItem) => `${showBreakTime(item.breakInTime)} ~ ${showBreakTime(item.breakOutTime)}`).join(' / ')">
            <div class="overflow-hidden text-ellipsis whitespace-nowrap"
              :class="getModifierColor(record?.modifiedBreakListLastModifierType)">
              <template v-for="(item, index) in record.breakList" :key="item">
                <span>
                  {{ showBreakTime(item.breakInTime) }} ~ {{ showBreakTime(item.breakOutTime) }}
                </span>
                <span v-if="index !== record.breakList.length - 1">
                  &nbsp;/&nbsp;
                </span>
              </template>
            </div>
          </a-tooltip>
          <<<<<<< HEAD=======>>>>>>> test
        </template>
      </template>
      <!-- <template v-if="column.dataIndex === 'fastCheckIn'">
        <a-button v-if="isFastCheckInSatify(record as AttendanceItem)" type="primary" @click="fastCheckIn(record as AttendanceItem)">
          {{ t('fastCheckIn') }}
        </a-button>
      </template> -->
      <template v-if="column.dataIndex === 'action'">
        <div v-if="record.employeeShiftId" class="flex gap-x-4 justify-center">
          <CarbonEdit class="cursor-pointer hover:text-blue" :width="calcWidth" :height="calcHeight"
            @click="viewAttendanceDetail(record as AttendanceItem)" />
          <CarbonDelete class="cursor-pointer" :width="calcWidth" :height="calcHeight"
            @click="handleDeleteShift(record as AttendanceItem)" />
          <<<<<<< HEAD=======>>>>>>> test
        </div>
      </template>
    </template>
  </a-table>

  <WorkTimeModal :show="isWorkTimeModalOpened" :attendance-item="attendanceItem"
    @update:show="isWorkTimeModalOpened = $event" @save-attendance="onSaveAttendance" />
</template>

    <style scoped lang="less">
    .tableAttendance {
      :deep(.ant-table-tbody > tr) {
        height: var(--row-height);

        td {
          padding: 4px 8px;
          font-size: var(--font-size);
        }
      }

      :deep(.ant-table-thead > tr) {
        th {
          padding: 8px;
          font-size: var(--font-size);
          font-weight: 600;
        }
      }

      :deep(.ant-table-tbody > tr.row-active) {
        background: #f2f8fd;

        td:first-child {
          border-left: 2px solid #b7d7f2;
        }

        td[rowspan]+td {
          border-left: 2px solid #b7d7f2;
        }

        td[rowspan]~td {
          border-top: 2px solid #b7d7f2 !important;
        }

        td[rowspan] {
          border-top: 2px solid #b7d7f2 !important;
        }

        td {
          background: #f2f8fd;
          border-bottom: 2px solid #b7d7f2 !important;
        }
      }

      :deep(.ant-table-tbody > tr.row-active.row-alone) {
        td {
          border-top: 2px solid #b7d7f2 !important;
        }
      }
    }

    <<<<<<< HEAD=======>>>>>>>test
  </style>