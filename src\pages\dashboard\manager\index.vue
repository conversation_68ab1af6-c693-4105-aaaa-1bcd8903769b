<script setup lang="ts">
import dayjs from 'dayjs'
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter'
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore'
import { usePagination } from 'vue-request'
import { LeftOutlined, RightOutlined } from '@ant-design/icons-vue'
import TotalTimeSection from '../components/total-time-section.vue'
import ProjectTab from './components/project-tab.vue'
import AttendanceTable from '~/pages/dashboard/components/attendance-table.vue'
import TodayAttendanceSection from '~/pages/dashboard/components/today-attendance-section.vue'
import DashboardClock from '~/pages/dashboard/components/dashboard-clock.vue'
import type { AttendanceItem, AttendanceSearchParams, AttendanceUpdateParams, CheckInParams, CheckOutParams, FastCheckInParams } from '~@/api/attendance'
import { checkInApi, checkInByShiftIdApi, getAttendanceByDateApi, sendRequestByAttendanceItem, updateAttendanceApi, updateBreakInApi, updateBreakOutApi, updateCheckoutA<PERSON> } from '~@/api/attendance'
import logger from '~@/utils/logger'
import type { ManagedProjectItem } from '~@/api/company/project'
import { getManagedProjectsApi } from '~@/api/company/project'

defineOptions({
  name: 'Workplace',
})
dayjs.extend(isSameOrAfter)
dayjs.extend(isSameOrBefore)

const { t } = useI18n()
const messageNotify = useMessage()

// State Variables
const selectedDate = ref<string>(dayjs().format('YYYY-MM-DD'))
const startOfWeek = computed(() =>
  dayjs(selectedDate.value).subtract(5, 'days').format('YYYY-MM-DD'),
)
const endOfWeek = computed(() => dayjs(selectedDate.value).add(1, 'days').format('YYYY-MM-DD'))

const timekeepingDetailByDate = ref()
const isOpened = ref(false)
const attendanceTableData = ref<AttendanceItem[]>([])
const currentIndex = ref(0)
const currentTime = ref(dayjs())
const today = ref(dayjs())
const searchDate = ref<dayjs.Dayjs>(dayjs())
const projectId = ref<string>('')
const managedProjects = ref<ManagedProjectItem[]>([])
const todayAttendanceData = ref<AttendanceItem[]>([])
const isShowProductionCost = ref(false)
const activeKey = ref('1')
const typeOfSort = ref('descend')
const params: AttendanceSearchParams = {
  fromDate: startOfWeek.value,
  toDate: endOfWeek.value,
  pageNum: 1,
  pageSize: 50,
}

const totalWorktimeOfDay = computed(() => {
  return todayAttendanceData.value.reduce((total, item) => {
    return total + (item.totalWorkTime ?? 0)
  }, 0)
})
const totalOvertimeOfDay = computed(() => {
  return todayAttendanceData.value.reduce((total, item) => {
    return total + (item.totalOverTime ?? 0)
  }, 0)
})

const todayAttendanceDate = computed(() => {
  return todayAttendanceData.value.filter((item: AttendanceItem) => item.workingDate === today.value.format('YYYY-MM-DD'))
})

const currentAttendance = computed(() => {
  const idx = todayAttendanceDate.value.findIndex((item: AttendanceItem) => !!item.checkInTime && item.checkOutTime === null)
  if (idx !== -1) {
    currentIndex.value = idx
    return todayAttendanceDate.value[idx]
  }
  return undefined
})

setInterval(() => {
  today.value = dayjs()
  currentTime.value = dayjs()
}, 1000)

function initTodayAttendanceData(data: AttendanceItem[]) {
  if (selectedDate.value !== today.value.format('YYYY-MM-DD'))
    return

  const employeeShift = data.filter((item: AttendanceItem) => item.workingDate === today.value.format('YYYY-MM-DD'))
  if (employeeShift) {
    todayAttendanceData.value = [
      ...employeeShift,
    ]
    todayAttendanceData.value.sort((a: AttendanceItem, b: AttendanceItem) => {
      if (a.createTime && b.createTime)
        return a.createTime.localeCompare(b.createTime) * -1

      return 0
    })
  }
}

const attendanceTableDataMap = ref<Map<string, AttendanceItem[]>>(new Map())

function createAttendanceTableDataMap(data: AttendanceItem[]) {
  const existingData = new Map<string, AttendanceItem[]>()
  data.forEach((item: AttendanceItem) => {
    if (item.workingDate) {
      if (existingData.has(item.workingDate))
        existingData.get(item.workingDate)?.push(item)

      else
        existingData.set(item.workingDate, [item])
    }
  })
  return existingData
}

// Insert fake attendance table data
function insertFakeAttendanceTableData(data: AttendanceItem[]) {
  attendanceTableDataMap.value = createAttendanceTableDataMap(data)

  let endDay = dayjs(endOfWeek.value)
  const startDay = dayjs(startOfWeek.value)
  attendanceTableData.value = []
  while (endDay.isAfter(startDay) || endDay.isSame(startDay, 'day')) {
    const date = endDay.format('YYYY-MM-DD')
    if (!attendanceTableDataMap.value.has(date)) {
      attendanceTableData.value.push({
        workingDate: date,
        createTime: dayjs(date).format('YYYY-MM-DD HH:mm:ss'),
      })
    }
    else {
      attendanceTableDataMap.value.get(date)?.forEach((item: AttendanceItem) => {
        attendanceTableData.value.push(item)
      })
    }
    endDay = endDay.subtract(1, 'day')
  }
  attendanceTableData.value.sort((a: AttendanceItem, b: AttendanceItem) => {
    return (a.createTime ?? '').localeCompare(b.createTime ?? '') * -1
  })
}

async function queryData(params: AttendanceSearchParams) {
  try {
    const { data, status } = await getAttendanceByDateApi(params)
    if (status === 200) {
      insertFakeAttendanceTableData(data?.items as AttendanceItem[])
      initTodayAttendanceData(data?.items ?? [])
      return data
    }
    else {
      insertFakeAttendanceTableData([])
      initTodayAttendanceData([])
    }
  }
  catch (e) {
    logger.error(e)
  }
}

const {
  run,
  refresh,
} = usePagination(queryData, {
  defaultParams: [params],
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
    totalKey: 'totalRecords',
  },
})

async function onChangeWeek(type: string) {
  if (type === 'next')
    selectedDate.value = dayjs(selectedDate.value).add(7, 'days').format('YYYY-MM-DD')

  else selectedDate.value = dayjs(selectedDate.value).subtract(7, 'days').format('YYYY-MM-DD')
  const params: AttendanceSearchParams = {
    fromDate: startOfWeek.value,
    toDate: endOfWeek.value,
    pageNum: 1,
    pageSize: 50,
  }
  run(params)
}

async function attendanceDailyRequest(employeeShiftId: string) {
  try {
    const { status, code } = await sendRequestByAttendanceItem(employeeShiftId)
    if (status === 200) {
      refresh()
      messageNotify.success(t(code))
    }
    else {
      messageNotify.error(t(code))
    }
  }
  catch (e) {
    logger.error(e)
  }
}

const isLoading = shallowRef(false)

async function normalCheckIn(params: CheckInParams) {
  if (isLoading.value)
    return

  isLoading.value = true
  const { status, message } = await checkInApi(params)
  if (status === 200) {
    selectedDate.value = dayjs().format('YYYY-MM-DD')
    refresh()
    messageNotify.success(message)
  }
  else {
    messageNotify.error(message)
  }
  isLoading.value = false
}

async function fastCheckIn(employeeShiftId: string, params: FastCheckInParams) {
  if (isLoading.value)
    return
  try {
    isLoading.value = true
    const { status, message } = await checkInByShiftIdApi(employeeShiftId, params)
    if (status === 200) {
      selectedDate.value = dayjs().format('YYYY-MM-DD')
      refresh()
      messageNotify.success(message)
    }
    else {
      messageNotify.error(message)
    }
  }
  catch (e) {
  }
  finally {
    isLoading.value = false
  }
}

async function breakIn(employeeShiftId: string) {
  if (isLoading.value)
    return

  try {
    isLoading.value = true
    const params: any = {}
    const { status, message } = await updateBreakInApi(employeeShiftId, params)
    if (status === 200) {
      refresh()
      selectedDate.value = dayjs().format('YYYY-MM-DD')
      messageNotify.success(message)
    }
    else {
      messageNotify.error(message)
    }
  }
  catch (e) {
  }
  finally {
    isLoading.value = false
  }
}

async function breakOut(employeeShiftId: string) {
  if (isLoading.value)
    return

  try {
    isLoading.value = true
    const params: any = {}
    const { status, message } = await updateBreakOutApi(employeeShiftId, params)
    if (status === 200) {
      refresh()
      selectedDate.value = dayjs().format('YYYY-MM-DD')
      messageNotify.success(message)
    }
    else {
      messageNotify.error(message)
    }
  }
  catch (e) {
  }
  finally {
    isLoading.value = false
  }
}

async function checkOut(employeeShiftId: string, params: CheckOutParams) {
  if (isLoading.value)
    return

  try {
    isLoading.value = true
    const { status, message } = await updateCheckoutApi(employeeShiftId, params)
    if (status === 200) {
      selectedDate.value = dayjs().format('YYYY-MM-DD')
      refresh()
      messageNotify.success(message)
    }
    else {
      messageNotify.error(message)
    }
  }
  catch (e) {
  }
  finally {
    isLoading.value = false
  }
}

function setTimekeepingDetailByDate(data: any) {
  timekeepingDetailByDate.value = data
  isOpened.value = true
}

function refreshAttendance() {
  refresh()
}

async function updateAttendanceItem(employeeShiftId: string, params: AttendanceUpdateParams) {
  try {
    const { status, message } = await updateAttendanceApi(employeeShiftId, params)
    if (status === 200) {
      await refresh()
      messageNotify.success(message)
    }
  }
  catch (e) {
  }
}

async function requestApproval(employeeShiftId: string) {
  if (isLoading.value)
    return

  isLoading.value = true
  try {
    const { status, message } = await sendRequestByAttendanceItem(employeeShiftId)
    if (status === 200) {
      refresh()
      messageNotify.success(message)
    }
    else {
      messageNotify.error(message)
    }
  }
  catch (e) {
  }
  finally {
    isLoading.value = false
  }
}

const projectOptions = computed(() => {
  const projectOp = managedProjects.value.map((project: ManagedProjectItem) => ({
    label: project.name,
    value: project.id,
  }))
  return [{ value: '', label: t('all') }, ...projectOp]
})

// Lifecycle Hooks
onMounted(async () => {
  const res = await Promise.all([getManagedProjectsApi()])
  managedProjects.value = res[0].data?.items ?? []
})

onActivated(() => {
})
</script>

<template>
  <page-container>
    <div class="flex flex-col gap-[24px] p-2">
      <!-- Grid container với responsive columns -->
      <div class="grid grid-cols-1 lg:grid-cols-4 gap-4">
        <!-- DashboardClock component -->
        <div class="col-span-1 lg:col-span-2 xl:col-span-1 bg-white shadow-sm hover:shadow-lg rounded-lg">
          <DashboardClock :current-attendance="currentAttendance" @normal-check-in="normalCheckIn" @break-in="breakIn"
            @break-out="breakOut" @check-out="checkOut" />
        </div>

        <!-- TodayAttendanceSection -->
        <div class="col-span-1 lg:col-span-2 xl:col-span-2 bg-white shadow-sm hover:shadow-lg rounded-lg">
          <TodayAttendanceSection :today-attendance-data="todayAttendanceData"
            :current-attendance-item-id="currentAttendance?.employeeShiftId ?? ''" :current-index="currentIndex"
            @update-attendance-item="updateAttendanceItem" @request-approval="requestApproval"
            @fast-check-in="fastCheckIn" />
        </div>

        <!-- Total time section -->
        <div class="col-span-1 lg:col-span-2 xl:col-span-1 bg-white shadow-sm hover:shadow-lg rounded-lg p-2">
          <TotalTimeSection :total-worktime-of-day="totalWorktimeOfDay" :total-overtime-of-day="totalOvertimeOfDay" />
        </div>
      </div>

      <!-- Attendance table section -->
      <div class="w-full">
        <!-- AttendanceTable Component -->
        <div class="h-[200px] mb-4">
          <a-tabs v-model:active-key="activeKey" type="card">
            <template #rightExtra>
              <div v-if="activeKey === '1'" class="flex items-center gap-4">
                <!-- <div class="flex items-center gap-2">
                  <a-switch v-model:checked="isShowProductionCost" :checked-children="t('productionCost')" :un-checked-children="t('averageCost')" />
                </div> -->
                <ProjectSelect v-model:value="projectId" class="w-[200px]" :options="projectOptions" />
                <div class="flex items-center text-gray-500">
                  <LeftOutlined class="flex justify-center w-6 h-6 bg-white rounded-full" @click="
                    searchDate = dayjs(searchDate).subtract(1, 'day');
                  " />
                  <a-date-picker v-model:value="searchDate" :allow-clear="false"
                    :format="(value: dayjs.Dayjs) => `${value.format('YYYY-MM-DD')}`" class="search-date" />
                  <RightOutlined class="flex justify-center w-6 h-6 bg-white rounded-full" @click="
                    searchDate = dayjs(searchDate).add(1, 'day');
                  " />
                </div>
              </div>
            </template>
            <a-tab-pane key="1" :tab="t('project-management')">
              <ProjectTab :search-date="searchDate" :project-id="projectId"
                :is-show-production-cost="isShowProductionCost" />
            </a-tab-pane>
            <a-tab-pane key="2" :tab="t('attendance-detail')">
              <a-row :span="24">
                <a-col :span="24">
                  <div class="flex justify-between items-center w-fit h-[50px] text-[1rem] font-500">
                    <span class="mr-[14px]">
                      {{ t('dashboard.workplace.tableTitle') }}:
                    </span>
                    <a-avatar src="/icon/left_outlined.svg" shape="square" class="p-1 cursor-pointer"
                      @click="onChangeWeek('prev')" />
                    <span>
                      {{ dayjs(startOfWeek).format('MM/DD') }} ~
                      {{ dayjs(endOfWeek).format('MM/DD') }}
                    </span>
                    <a-avatar src="/icon/right_outlined.svg" shape="square" class="p-1 cursor-pointer"
                      @click="onChangeWeek('next')" />
                    <a-avatar src="/icon/calendar_attendance_icon.svg" shape="square"
                      class="w-[1.2rem] h-[1.2rem] ml-[2px]" />
                  </div>
                </a-col>
                <a-col :span="24">
                  <AttendanceTable :data-source="attendanceTableData" :type-of-sort="typeOfSort"
                    @on-timekeeping-detail-by-date="setTimekeepingDetailByDate"
                    @attendance-daily-request="attendanceDailyRequest" @refresh-attendance="refreshAttendance" />
                </a-col>
              </a-row>
            </a-tab-pane>
          </a-tabs>
        </div>
      </div>
    </div>
  </page-container>
</template>

<style scoped lang="less">
// section clock and detail
.ant-btn-primary:disabled {
  background-color: #CDCECD;
  color: #74797A !important;
  border-radius: 4px;
}

.detail-title {
  font-size: 1rem;
  font-weight: 500;
  font: Noto Sans JP;
  margin-right: 2px;
}

.attend-detail::-webkit-scrollbar {
  width: 7px;
  margin-bottom: '12px';
  height: '280px';
}

.attend-detail::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background-color: rgba(0, 0, 0, .5);
  box-shadow: 0 0 1px rgba(255, 255, 255, .5);
}

.icon-circle-over {
  background-color: #FCBF91;
  padding: 1rem;
  width: 4.5rem;
  height: 4.5rem;
}

.icon-circle-time {
  background-color: #CEE1F1;
  padding: 1.25rem;
  width: 4.5rem;
  height: 4.5rem;
}

.total-timer {
  font-size: 2rem;
  font-weight: 500;
}

.total-text {
  color: #74797A;
  font-size: 1rem;
  font-weight: 500;
}

.date {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

/* Custom scrollbar cho table overflow */
.overflow-x-auto::-webkit-scrollbar {
  height: 6px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 6px;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Responsive styles */
@media (max-width: 640px) {

  :deep(.ant-table-thead > tr > th),
  :deep(.ant-table-tbody > tr > td) {
    padding: 8px 4px;
    white-space: nowrap;
  }

  .total-timer {
    font-size: 1.5rem;
  }

  .icon-circle-time,
  .icon-circle-over {
    width: 3.5rem;
    height: 3.5rem;
    padding: 0.75rem;
  }
}

@media (max-width: 768px) {
  .total-text {
    font-size: 0.875rem;
  }
}

.search-date {
  border: none;
  background: none;
  box-shadow: none;

  :deep(.ant-picker-suffix) {
    display: none;
  }

  :deep(input) {
    cursor: pointer;
    width: 82px;
  }
}
</style>
