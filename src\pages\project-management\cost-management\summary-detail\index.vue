<!-- eslint-disable antfu/top-level-function -->
<script lang="ts" setup>
import { computed, onMounted, reactive, ref } from 'vue'
import {
  DeleteOutlined,
  EditOutlined,
  FileTextOutlined,
  ImportOutlined,
  ReloadOutlined,
  SearchOutlined,
  UploadOutlined,
} from '@ant-design/icons-vue'
import { usePagination } from 'vue-request'
import type { SelectValue } from 'ant-design-vue/es/select'
import dayjs from 'dayjs'
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter'
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore'

import InvoiceModal from './components/InvoiceModal.vue'
import type { InputCostData, InputCostItem, InputCostItemParams, InputCostItemQueryParams } from '~@/api/input-cost-item'
import { createInputCostItem, getInputCostItemApi, updateInputCostItemApi } from '~@/api/input-cost-item'
import { formatCurrency, formatNo, formatPercent, parseDate } from '~@/utils/apiTimer'
import type { CostItem, GetCostItemParams } from '~@/api/company/cost-item'
import { getCostItem } from '~@/api/company/cost-item'
import type { GetVendorParams } from '~@/api/company/vendor'
import { getVendor } from '~@/api/company/vendor'
import { getProjectComboApi } from '~@/api/company/project'
import type { QueryParams } from '~@/api/common-params'
import type { ConstructionItem } from '~@/api/construction'
import { getConstructionByProjectIdApi } from '~@/api/construction'
import type { CostCategoryItem, GetCostCategoryParams } from '~@/api/company/cost-category'
import { getCostCategory } from '~@/api/company/cost-category'
import type { InputCost } from '~@/api/invoice'

dayjs.extend(isSameOrAfter)
dayjs.extend(isSameOrBefore)
// interface Pagination {
//   pageNum: number
//   pageSize: number
//   totalRecords: number
// }

const activeTab = ref(['EMPLOYEE'])
const { t } = useI18n()
const { roles } = useUserStore()
const { checkEmployeeCostVisibleAll } = useMenuPermission()

// Table data

// Modals
const descriptionModalVisible = ref(false)
const isVisible = ref(false)
const itemModalVisible = ref(false)
const editMode = ref(false)
const selectedItem = ref<InputCostItem | null>(null)
const currentInvoice = ref<InputCost>()
const route = useRoute()
const selectedProjectId = ref(route.query.id as string)
const messageNotify = useMessage()
const isShowProductCost = ref(true)

const selectedCategoryId = ref<string>()
const categoryOptions = ref<CostCategoryItem[]>([])

interface ItemState {
  itemId?: string
  itemName?: string
  categoryId?: string
  categoryName?: string
}

const itemState = reactive<ItemState>({
  itemId: undefined,
  itemName: undefined,
  categoryId: undefined,
  categoryName: undefined,
})

interface VendorState {
  vendorId?: string
  vendorName?: string
}

const vendorState = reactive<VendorState>({
  vendorId: undefined,
  vendorName: undefined,
})

interface FormState {
  constructionId?: string
  transactionDate?: string
  unit?: string
  vendorName?: string
  quantity?: number
  price?: number
  taxRate?: number
  totalNonTaxed?: number
  totalTaxed?: number
  description?: string
}

// Form
const formState = reactive<FormState>({
  constructionId: undefined,
  transactionDate: undefined,
  unit: undefined,
  vendorName: undefined,
  quantity: undefined,
  price: undefined,
  taxRate: undefined,
  totalNonTaxed: undefined,
  totalTaxed: undefined,
  description: undefined,
})

const filterParams = reactive<InputCostItemQueryParams>({
  pageNum: 1,
  pageSize: 20,
  projectId: undefined,
  constructionId: undefined,
  inputCostId: undefined,
  categoryId: undefined,
  vendorId: undefined,
  dateFrom: dayjs().startOf('month').format('YYYY-MM-DD'),
  dateTo: dayjs().endOf('month').format('YYYY-MM-DD'),
})

const columns = ref<any>([])

// Table columns
const commonColumns = reactive<any>([
  {
    title: t('transaction-date'),
    dataIndex: 'transactionDate',
    key: 'transactionDate',
    width: 120,
  },
  {
    title: t('item-name'),
    dataIndex: 'itemName',
    key: 'itemName',
    width: 150,
    sorter: (a: InputCostItem, b: InputCostItem) => (a.itemName || '').localeCompare(b.itemName || ''),
  },
  {
    title: t('vendor'),
    dataIndex: 'vendorName',
    key: 'vendorName',
    width: 150,
    sorter: (a: InputCostItem, b: InputCostItem) => (a.vendorName || '').localeCompare(b.vendorName || ''),
  },
  // {
  //   title: 'Category',
  //   dataIndex: 'categoryName',
  //   key: 'category',
  //   width: 150,
  //   sorter: (a: InputCostItem, b: InputCostItem) => (a.category || '').localeCompare(b.category || ''),
  // },
  {
    title: t('price'),
    dataIndex: 'price',
    key: 'price',
    width: 120,
    sorter: (a: InputCostItem, b: InputCostItem) => a.price - b.price,
  },
  {
    title: t('quantity'),
    dataIndex: 'quantity',
    key: 'quantity',
    width: 100,
    sorter: (a: InputCostItem, b: InputCostItem) => a.quantity - b.quantity,
  },
  {
    title: t('unit'),
    dataIndex: 'unit',
    key: 'unit',
    width: 100,
  },
  {
    title: t('tax-rate'),
    dataIndex: 'taxRate',
    key: 'taxRate',
    width: 100,
  },
  {
    title: t('price-before-tax'),
    dataIndex: 'totalNonTaxed',
    key: 'totalNonTaxed',
    width: 150,
    sorter: (a: InputCostItem, b: InputCostItem) => (a.totalNonTaxed ?? 0) - (b.totalNonTaxed ?? 0),
  },
  {
    title: t('price-after-tax'),
    dataIndex: 'totalTaxed',
    key: 'totalTaxed',
    width: 150,
    sorter: (a: InputCostItem, b: InputCostItem) => (a.totalTaxed ?? 0) - (b.totalTaxed ?? 0),
  },
  {
    title: t('original-input-cost-number'),
    dataIndex: 'originalInputCostNumber',
    key: 'originalInputCostNumber',
  },
  {
    title: t('description'),
    dataIndex: 'description',
    key: 'description',
    width: 120,
  },
  {
    title: t('actions'),
    dataIndex: 'actions',
    key: 'actions',
    width: 100,
    fixed: 'right',
  },
])

const employeeColumns = reactive<any>([
  {
    title: t('rankName'),
    dataIndex: 'itemName',
    key: 'itemName',
  },
  {
    title: t('manHours'),
    dataIndex: 'quantity',
    key: 'quantity',
  },
  {
    title: t('unitPrice'),
    dataIndex: 'unitPrice',
    key: 'unitPrice',
  },
  {
    title: t('amount'),
    dataIndex: 'employeeAmount',
    key: 'employeeAmount',
  },
])

const outsourceColumns = reactive<any>([
  {
    title: t('outsourceName'),
    dataIndex: 'itemName',
    key: 'itemName',
  },
  {
    title: t('manHours'),
    dataIndex: 'quantity',
    key: 'quantity',
  },
  {
    title: t('unitPrice'),
    dataIndex: 'unitPrice',
    key: 'unitPrice',
  },
  {
    title: t('amount'),
    dataIndex: 'outsourceAmount',
    key: 'outsourceAmount',
  },
])

async function queryData(params: InputCostItemQueryParams) {
  if (!selectedCategoryId.value)
    return
  const { data } = await getInputCostItemApi(selectedCategoryId.value, params)

  return data
}
const {
  data,
  refresh,
  loading,
  run,
  current,
  pageSize,
  total,
} = usePagination(queryData, {
  defaultParams: [filterParams],
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
    totalKey: 'totalRecords',
  },
})
const projectParams = reactive<QueryParams>({
  pageNum: 1,
  pageSize: 8,
})
async function queryProject(params: QueryParams) {
  const { data } = await getProjectComboApi(params)
  return data
}
const {
  data: projectData,
} = usePagination(queryProject, {
  defaultParams: [projectParams],
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
    totalKey: 'totalRecords',
  },
})
const projectOptions = computed(() => {
  return projectData.value?.items || []
})

const inputCostItems = computed(() => {
  return data.value?.items || []
})

const pagination = computed(() => {
  return {
    pageNum: current.value,
    pageSize: pageSize.value,
    totalRecords: total.value,
  }
})

// Handlers
function handleSearch() {
  run({
    ...filterParams,
    pageNum: 1,
    pageSize: 20,
  })
}

function resetInputCostItemQueryParams() {
  filterParams.projectId = undefined
  filterParams.constructionId = undefined
  filterParams.inputCostId = undefined
  filterParams.categoryId = undefined
  filterParams.vendorId = undefined
  filterParams.dateFrom = undefined
  filterParams.dateTo = undefined
  handleSearch()
}

function changeTab(categoryId: string, categoryCode: string) {
  selectedCategoryId.value = categoryId
  if (categoryCode === 'EMPLOYEE')
    columns.value = employeeColumns

  else if (categoryCode === '')
    columns.value = outsourceColumns

  else
    columns.value = commonColumns

  run({
    ...filterParams,
  })
}

function handlePageChange(page: number, pageSize: number) {
  run({
    ...filterParams,
    pageNum: page,
    pageSize,
  })
}

function handleSizeChange(current: number, size: number) {
  run({
    pageNum: current,
    pageSize: size,
  })
}

function showDescriptionModal(record: InputCostItem) {
  selectedItem.value = record
  descriptionModalVisible.value = true
}

function resetForm() {
  formState.constructionId = undefined
  formState.transactionDate = undefined
  formState.unit = undefined
  formState.vendorName = undefined
  formState.quantity = undefined
  formState.price = undefined
  formState.taxRate = undefined
  formState.description = undefined
  formState.totalNonTaxed = undefined
  formState.totalTaxed = undefined

  // Reset item and vendor states
  itemState.itemId = undefined
  itemState.itemName = undefined
  itemState.categoryId = undefined
  itemState.categoryName = undefined

  vendorState.vendorId = undefined
  vendorState.vendorName = undefined
}

function showAddItemModal() {
  // Reset form
  resetForm()
  editMode.value = false
  itemModalVisible.value = true
}

function showEditModal(record: InputCostItem) {
  selectedItem.value = record
  editMode.value = true
  itemModalVisible.value = true
  // formState.constructionId = record.constructionId
  formState.transactionDate = parseDate(record.transactionDate)
  formState.unit = record.unit ?? undefined
  formState.vendorName = record.vendorName ?? undefined
  formState.quantity = record.quantity
  formState.price = record.price
  formState.taxRate = record.taxRate ?? undefined
  formState.description = record.description ?? undefined
}

function showDeleteModal(record: InputCostItem) {
  selectedItem.value = record
  // deleteModalVisible.value = true
}

async function handleItemSave() {
  // Validate required fields for create mode
  if (!editMode.value) {
    if (!formState.constructionId || !formState.transactionDate || !formState.unit || !itemState.itemId) {
      messageNotify.error('error.requiredFields')
      return
    }
  }

  // Calculate derived values - Fixed tax calculation
  // const totalNonTaxed = (formState.price || 0) * (formState.quantity || 0)
  // const totalTaxed = totalNonTaxed * (1 + (formState.taxRate || 0) / 100)

  if (editMode.value) {
    const params: InputCostItemParams = {
      unit: formState.unit ?? '',
      constructionId: formState.constructionId ?? '',
      quantity: formState.quantity ?? 0,
      price: formState.price ?? 0,
      taxRate: formState.taxRate ?? 0,
      description: formState.description ?? '',
      itemId: selectedItem.value?.itemId ?? '',
      vendorId: selectedItem.value?.vendorId ?? '',
      transactionDate: formState.transactionDate ?? '',
    }
    if (!selectedItem.value || !formState.transactionDate || !formState.unit)
      return

    const { status } = await updateInputCostItemApi(selectedItem.value?.inputCostItemId ?? '', params)
    if (status === 200) {
      refresh()
      messageNotify.success('Item updated successfully')
    }
    else {
      messageNotify.error('Item update failed')
    }
  }
  else {
    // Add new item
    const params: InputCostData = {
      constructionId: formState.constructionId ?? '',
      transactionDate: formState.transactionDate ?? '',
      unit: formState.unit ?? '',
      quantity: formState.quantity ?? 0,
      price: formState.price ?? 0,
      taxRate: formState.taxRate ?? 0,
      description: formState.description ?? '',
      item: {
        itemId: itemState.itemId ?? '',
        itemName: itemState.itemName ?? '',
      },
      vendor: {
        vendorId: vendorState.vendorId ?? '',
        vendorName: vendorState.vendorName ?? '',
      },
    }
    const { status } = await createInputCostItem(params)
    if (status === 200) {
      messageNotify.success('Item added successfully')
      refresh()
    }
    else {
      messageNotify.error('Item add failed')
    }
  }
  itemModalVisible.value = false
}
function handleFileImport() {
  isVisible.value = true
}

async function queryItem(params: GetCostItemParams) {
  const { data } = await getCostItem(params)
  return data
}

async function fetchCategory(params: GetCostCategoryParams) {
  const { data } = await getCostCategory(params)
  categoryOptions.value = data?.items ?? []
}

const {
  data: itemData,
} = usePagination(queryItem, {
  defaultParams: [{
    pageNum: 1,
    pageSize: 10,
  }],
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
    totalKey: 'totalRecords',
  },
})

async function queryVendor(params: GetVendorParams) {
  const { data } = await getVendor(params)
  return data
}

const {
  data: vendorData,
} = usePagination(queryVendor, {
  defaultParams: [{
    pageNum: 1,
    pageSize: 10,
  }],
  pagination: {
    currentKey: 'pageNum',
    pageSizeKey: 'pageSize',
    totalKey: 'totalRecords',
  },
})

const itemOptions = computed(() => {
  return itemData.value?.items ?? []
})

const vendorOptions = computed(() => {
  return vendorData.value?.items ?? []
})

const constructionOptions = ref<ConstructionItem[]>()

function onItemChange(itemId: SelectValue) {
  if (typeof itemId !== 'string')
    return
  const item = itemOptions.value.find((item: CostItem) => item.itemId === itemId)
  itemState.itemId = itemId
  itemState.itemName = item?.itemName || ''
  itemState.categoryId = item?.categoryId || ''
  itemState.categoryName = item?.categoryName || ''
}

// Add vendor change handler
function onVendorChange(vendorId: SelectValue) {
  if (typeof vendorId !== 'string')
    return
  const vendor = vendorOptions.value.find((vendor: any) => vendor.vendorId === vendorId)
  vendorState.vendorId = vendorId
  vendorState.vendorName = vendor?.vendorName || ''
}

async function onProjectChange(value: SelectValue) {
  if (typeof value !== 'string')
    return
  const { data, status } = await getConstructionByProjectIdApi(value)
  if (status === 200)
    constructionOptions.value = data?.constructions ?? []
}

async function handleFilterProject() {
  const projectId = filterParams.projectId ?? ''
  onProjectChange(projectId)
}

// Add summary calculation functions
function calculateTotalManHours() {
  return inputCostItems.value.reduce((sum, item) => sum + (item.quantity || 0), 0)
}

const handleShowOriginalCost = () => {
  console.log('isShowProductCost', isShowProductCost.value)
}

const totalAmount = computed(() => {
  if (isShowProductCost.value)
    return inputCostItems.value.reduce((sum, item) => sum + (item.totalNonTaxed ?? 0), 0)
  else
    return inputCostItems.value.reduce((sum, item) => sum + (item.totalAverageAmount ?? 0), 0)
})

// Ngay thang trong filter bao gồm ngày tháng cảu tháng này
function isBetweenFilterDate() {
  const currentStartDate = dayjs().startOf('month')
  const currentEndDate = dayjs().endOf('month')
  const filterStartDate = filterParams.dateFrom
  const filterEndDate = filterParams.dateTo
  if (filterStartDate && filterEndDate)
    return currentStartDate.isSameOrAfter(dayjs(filterStartDate), 'day') && currentEndDate.isSameOrBefore(dayjs(filterEndDate), 'day')

  return false
}

const isEmployeeCostVisibleAll = computed(() => {
  const rule = isBetweenFilterDate() ? 'RULE_1' : 'RULE_2'
  return roles.some(role => checkEmployeeCostVisibleAll(role.roleId, rule))
})

// Lifecycle hooks
onMounted(async () => {
  if (selectedProjectId.value)
    filterParams.projectId = selectedProjectId.value

  await fetchCategory({
    keyword: undefined,
    parentId: undefined,
    pageNum: 1,
    pageSize: 100,
  })
  selectedCategoryId.value = categoryOptions.value.find((item: CostCategoryItem) => item.categoryCode === 'EMPLOYEE')?.categoryId
  columns.value = employeeColumns
  refresh()
})
</script>

<template>
  <div class="cost-management-container p-4">
    <!-- Filter Section -->
    <div class="flex justify-between items-center">
      <div class="filter-section mb-6 flex flex-wrap gap-4 items-end">
        <div class="filter-group">
          <label class="block text-sm mb-1">{{ t('project') }}</label>
          <a-select v-model:value="filterParams.projectId" style="width: 200px"
            :placeholder="t('placeholder.select-data', { msg: t('project') })" :options="projectOptions"
            :field-names="{ label: 'name', value: 'id' }" allow-clear @change="handleFilterProject" />
        </div>

        <div class="filter-group">
          <label class="block text-sm mb-1">{{ t('main-construction') }}</label>
          <a-select v-model:value="filterParams.constructionId" style="width: 200px"
            :placeholder="t('placeholder.select-data', { msg: t('main-construction') })" :options="constructionOptions"
            :field-names="{ label: 'constructionName', value: 'constructionId' }" allow-clear />
        </div>

        <div class="filter-group">
          <label class="block text-sm mb-1">{{ t('from') }}</label>
          <a-date-picker v-model:value="filterParams.dateFrom" value-format="YYYY-MM-DD" format="YYYY-MM-DD"
            style="width: 150px" />
        </div>

        <div class="filter-group">
          <label class="block text-sm mb-1">{{ t('to') }}</label>
          <a-date-picker v-model:value="filterParams.dateTo" value-format="YYYY-MM-DD" format="YYYY-MM-DD"
            style="width: 150px" />
        </div>

        <div class="filter-actions flex gap-2">
          <a-button type="primary" class="flex items-center" @click="handleSearch">
            <template #icon>
              <SearchOutlined />
            </template>
            {{ t('button.search') }}
          </a-button>
          <a-button class="flex items-center" @click="resetInputCostItemQueryParams">
            <template #icon>
              <ReloadOutlined />
            </template>
            {{ t('button.reset') }}
          </a-button>
        </div>
      </div>
      <div v-if="isEmployeeCostVisibleAll" class="flex gap-2">
        <a-switch v-model:checked="isShowProductCost" :checked-children="t('productionCost')"
          :un-checked-children="t('averageCost')" @change="handleShowOriginalCost" />
      </div>
    </div>

    <!-- Tab Navigation -->
    <div class="tab-navigation border-b mb-4">
      <a-menu v-model:selected-keys="activeTab" mode="horizontal" class="border-none">
        <a-menu-item v-for="item in categoryOptions" :key="item.categoryCode"
          @click="changeTab(item.categoryId, item.categoryCode)">
          {{ item.categoryName }}
        </a-menu-item>
      </a-menu>
    </div>

    <!-- Data Table -->
    <a-table :columns="columns" :loading="loading" :data-source="inputCostItems" bordered size="small" :pagination="{
      current: pagination.pageNum,
      pageSize: pagination.pageSize,
      total: pagination.totalRecords,
      showSizeChanger: true,
      onChange: handlePageChange,
      onShowSizeChange: handleSizeChange,
    }" :row-key="(record: InputCostItem) => record.inputCostItemId ?? `temp-${Math.random()}`"
      :scroll="{ x: 'max-content' }">
      <!-- No. Column -->
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.dataIndex === 'no'">
          <span>{{ formatNo(index) }}</span>
        </template>
        <template v-if="column.dataIndex === 'transactionDate'">
          <span>{{ parseDate(record.transactionDate) }}</span>
        </template>

        <!-- Price/Tax Columns - Show Currency Symbol -->
        <!-- <template v-else-if="['unitPrice', 'totalNonTaxed', 'totalTaxed'].includes(`${column.dataIndex}`)">
          <div class="flex justify-between">
            <span>{{ formatCurrency(record[`${column.dataIndex}`]) }}</span>
            <span>¥</span>
          </div>
        </template> -->

        <!-- Tax Rate Column -->
        <template v-else-if="column.dataIndex === 'taxRate'">
          {{ formatPercent(record.taxRate) }}
        </template>

        <!-- Description Column -->
        <template v-else-if="column.dataIndex === 'description'">
          <a-button type="text" @click="showDescriptionModal(record as InputCostItem)">
            <template #icon>
              <FileTextOutlined />
            </template>
          </a-button>
        </template>

        <template v-if="column.dataIndex === 'unitPrice'">
          <div class="flex justify-between">
            <span v-if="isShowProductCost">{{ formatCurrency(record.price) }}</span>
            <span v-else>{{ formatCurrency(record.averagePrice) }}</span>
            <span>¥</span>
          </div>
        </template>

        <template v-if="column.dataIndex === 'employeeAmount'">
          <div class="flex justify-between">
            <span v-if="isShowProductCost">{{ formatCurrency(record.totalAverageAmount) }}</span>
            <span v-else>{{ formatCurrency(record.totalNonTaxed) }}</span>
            <span>¥</span>
          </div>
        </template>

        <template v-if="column.dataIndex === 'outsourceAmount'">
          <div class="flex justify-between">
            <span v-if="isShowProductCost">{{ formatCurrency(record.totalAverageAmount) }}</span>
            <span v-else>{{ formatCurrency(record.totalNonTaxed) }}</span>
            <span>¥</span>
          </div>
        </template>

        <template v-if="column.dataIndex === 'actions'">
          <a-button type="text" @click="showEditModal(record as InputCostItem)">
            <template #icon>
              <EditOutlined />
            </template>
          </a-button>
          <a-button type="text" @click="showDeleteModal(record as InputCostItem)">
            <template #icon>
              <DeleteOutlined />
            </template>
          </a-button>
        </template>
      </template>

      <!-- Summary Row for Employee Columns -->
      <template v-if="columns === employeeColumns || columns === outsourceColumns" #summary>
        <a-table-summary>
          <a-table-summary-row>
            <a-table-summary-cell :index="0" class="font-bold">
              {{ t('total') }}
            </a-table-summary-cell>
            <a-table-summary-cell :index="1" class="font-bold text-left">
              {{ calculateTotalManHours() }}
            </a-table-summary-cell>
            <!-- <a-table-summary-cell :index="2" class="font-bold text-right">
              <div class="flex justify-between">
                <span>{{ formatCurrency(calculateTotalApproximatePrice()) }}</span>
                <span>¥</span>
              </div>
            </a-table-summary-cell>
            <a-table-summary-cell :index="3" class="font-bold text-right">
              <div class="flex justify-between">
                <span>{{ formatCurrency(calculateTotalFixedPrice()) }}</span>
                <span>¥</span>
              </div>
            </a-table-summary-cell> -->
            <a-table-summary-cell :index="2" class="font-bold text-right">
              <!-- <div class="flex justify-between">
                <span>{{ formatCurrency(calculateTotalAveragePrice()) }}</span>
                <span>¥</span>
              </div> -->
            </a-table-summary-cell>
            <a-table-summary-cell :index="3" class="font-bold text-right">
              <div class="flex justify-between">
                <span>{{ formatCurrency(totalAmount) }}</span>
                <span>¥</span>
              </div>
            </a-table-summary-cell>
          </a-table-summary-row>
        </a-table-summary>
      </template>
    </a-table>

    <!-- Action Buttons -->
    <div class="action-buttons mt-4 flex gap-2">
      <a-button class="flex items-center" @click="showAddItemModal">
        <template #icon>
          <ImportOutlined />
        </template>
        {{ t('button.manual-import') }}
      </a-button>
      <a-button class="flex items-center" @click="handleFileImport">
        <template #icon>
          <UploadOutlined />
        </template>
        {{ t('button.image-import') }}
      </a-button>
    </div>

    <!-- Description Modal -->
    <a-modal v-model:visible="descriptionModalVisible" :title="t('item-description')" :footer="null" width="600px">
      <p v-if="selectedItem">
        {{ selectedItem.description }}
      </p>
    </a-modal>

    <!-- Add/Edit Item Modal -->
    <a-modal v-model:visible="itemModalVisible" :title="editMode ? t('button.edit') : t('button.add')" width="800px"
      @ok="handleItemSave">
      <a-form :model="formState" layout="vertical">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <a-form-item v-if="!editMode" :label="t('projectName')"
            :rules="[{ required: true, message: t('message.please-select', { msg: t('projectName') }) }]">
            <a-select v-model:value="selectedProjectId" :options="projectOptions"
              :field-names="{ label: 'name', value: 'id' }" @change="onProjectChange" />
          </a-form-item>
          <a-form-item v-if="!editMode" :label="t('construction')" name="constructionId"
            :rules="[{ required: true, message: t('message.please-select', { msg: t('construction') }) }]">
            <a-select v-model:value="formState.constructionId" :options="constructionOptions"
              :field-names="{ label: 'constructionName', value: 'constructionId' }" />
          </a-form-item>
          <a-form-item :label="t('transaction-date')" name="transactionDate"
            :rules="[{ required: true, message: t('message.please-select', { msg: t('transaction-date') }) }]">
            <a-date-picker v-model:value="formState.transactionDate" value-format="YYYY-MM-DD" format="YYYY-MM-DD"
              style="width: 100%" />
          </a-form-item>

          <a-form-item :label="t('unit')" name="unit"
            :rules="[{ required: true, message: t('message.please-select', { msg: t('unit') }) }]">
            <a-input v-model:value="formState.unit" />
          </a-form-item>

          <a-form-item :label="t('unit-price')" name="unitPrice">
            <a-input-number v-model:value="formState.price" :min="0" style="width: 100%" />
          </a-form-item>

          <a-form-item :label="t('quantity')" name="quantity">
            <a-input-number v-model:value="formState.quantity" :min="0" style="width: 100%" />
          </a-form-item>

          <a-form-item :label="t('tax-rate-percent')" name="taxRate" class="col-span-2">
            <a-input-number v-model:value="formState.taxRate" :min="0" :max="100" style="width: 100%" />
          </a-form-item>
        </div>
      </a-form>
      <a-form layout="vertical" :model="itemState">
        <a-form-item :label="t('item-name')" name="itemId"
          :rules="[{ required: true, message: t('message.please-select', { msg: t('item-name') }) }]">
          <a-select v-model:value="itemState.itemId" :options="itemOptions"
            :field-names="{ label: 'itemName', value: 'itemId' }" @change="onItemChange" />
        </a-form-item>
        <a-form-item :label="t('category')" name="categoryName">
          <a-input v-model:value="itemState.categoryName" disabled />
        </a-form-item>
      </a-form>
      <a-form name="vendorState" layout="vertical" :model="vendorState">
        <a-form-item :label="t('vendor')" name="vendorId">
          <a-select v-model:value="vendorState.vendorId" :options="vendorOptions"
            :field-names="{ label: 'vendorName', value: 'vendorId' }" @change="onVendorChange" />
        </a-form-item>
      </a-form>
      <a-form name="formSate" layout="vertical" :model="formState">
        <a-form-item :label="t('description')" name="description" class="col-span-1 md:col-span-2">
          <a-textarea v-model:value="formState.description" :rows="4" />
        </a-form-item>
      </a-form>
    </a-modal>
    <InvoiceModal v-model:open-modal="isVisible" :invoice="currentInvoice as InputCost" />
  </div>
</template>

<style scoped>
/* Additional styles can be added here if needed beyond Tailwind */
.cost-management-container {
  @apply bg-white;
}

/* Add active tab indicator styling */
:deep(.ant-menu-horizontal > .ant-menu-item-selected::after) {
  @apply border-blue-500 border-b-2;
}
</style>
