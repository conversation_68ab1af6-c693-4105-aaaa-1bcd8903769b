<script lang="ts" setup>
import type { Dayjs } from 'dayjs'
import type { SelectProps } from 'ant-design-vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import SummaryCard from '../components/summary-card.vue'
import EmployeeShiftTable from '../components/employee-shift-table.vue'
import type {
  AttendanceItem,
  AttendanceSearchParams,
} from '~@/api/attendance'
import { getAttendanceByDateApi, getAttendanceEmployeeSummaryApi } from '~@/api/attendance'
import logger from '~@/utils/logger'
import type { QueryParams } from '~@/api/common-params'
import type { ProjectComboItem } from '~@/api/company/project'
import { getProjectComboApi } from '~@/api/company/project'
import type { MonthlyRequestParams } from '~@/api/monthly-attendance'
import { requestApprovalMonthly } from '~@/api/monthly-attendance'

const props = defineProps<{
  pickedMonth: Dayjs

  fontSize: number
  rowHeight: number
}>()

const selectedDay = ref(props.pickedMonth ?? dayjs())

const modalVisible = ref(false)
const formRef = ref()
const scheduleLeft = ref()
const summaryCardRef = ref()
const attendanceDataByMonth = ref<AttendanceItem[]>([])
const attendanceTableDataMap = ref<Map<string, AttendanceItem[]>>(new Map())
const projectComboData = ref<SelectProps['options']>([])

const { height: scheduleLeftHeight } = useElementSize(scheduleLeft)
const totalWorkingDays = ref(0)
const totalAbsentDays = ref(0)
const totalWorkTime = ref(0)
const totalOverTime = ref(0)
const totalPaidLeaveUsed = ref(0)
const totalUnpaidLeaveUsed = ref(0)
const typeOfSort = ref('ascend')

const officeRequestFormstate = reactive<MonthlyRequestParams>({
  reportFrom: '',
  reportTo: '',
  description: '',
})

function createAttendanceTableDataMap(data: AttendanceItem[]) {
  const existingData = new Map<string, AttendanceItem[]>()
  data.forEach((item: AttendanceItem) => {
    if (item.workingDate) {
      if (existingData.has(item.workingDate))
        existingData.get(item.workingDate)?.push(item)
      else existingData.set(item.workingDate, [item])
    }
  })
  return existingData
}

function insertFakeAttendanceTableData(data: AttendanceItem[]) {
  attendanceTableDataMap.value = createAttendanceTableDataMap(data)
  const endDay = selectedDay.value.endOf('month')
  let startDay = selectedDay.value.startOf('month')
  attendanceDataByMonth.value = []

  while (startDay.isBefore(endDay) || startDay.isSame(endDay, 'day')) {
    const date = startDay.format('YYYY-MM-DD')

    if (!attendanceTableDataMap.value.has(date)) {
      attendanceDataByMonth.value.push({
        workingDate: date,
      })
    }
    else {
      attendanceTableDataMap.value
        .get(date)
        ?.forEach((item: AttendanceItem) => {
          attendanceDataByMonth.value.push(item)
        })
    }

    startDay = startDay.add(1, 'day')
  }
}

async function getSimpleProject() {
  const params: QueryParams = {
    pageNum: 1,
    pageSize: 1000,
  }
  try {
    const { data } = await getProjectComboApi(params)

    projectComboData.value = data?.items?.filter((item: ProjectComboItem) => {
      return item.isOffice
    })
  }
  catch (e) {
    logger.error(e)
  }
}

async function getAttendanceByDate() {
  const params: AttendanceSearchParams = {
    fromDate: selectedDay.value.startOf('month').format('YYYY-MM-DD'),
    toDate: selectedDay.value.endOf('month').format('YYYY-MM-DD'),
  }
  try {
    const { data } = await getAttendanceByDateApi(params)

    insertFakeAttendanceTableData(data?.items ?? [])
  }
  catch (e) {
    logger.error(e)
    throw e
  }
}

async function submitAttendanceToOffice() {
  try {
    officeRequestFormstate.reportFrom = selectedDay.value
      .startOf('month')
      .format('YYYY-MM-DD')
    officeRequestFormstate.reportTo = selectedDay.value
      .endOf('month')
      .format('YYYY-MM-DD')

    const data = await requestApprovalMonthly(officeRequestFormstate)
    if (data.message)
      message.success(data.message)

    onCancel()
  }
  catch (e) {
    logger.error(e)
  }
}

async function getAttendanceEmployeeSummary() {
  try {
    const { data, status } = await getAttendanceEmployeeSummaryApi({
      fromDate: selectedDay.value.startOf('month').format('YYYY-MM-DD'),
      toDate: selectedDay.value.endOf('month').format('YYYY-MM-DD'),
    })
    if (status === 200) {
      totalWorkingDays.value = data?.totalWorkingDays ?? 0
      totalAbsentDays.value = data?.totalAbsentDays ?? 0
      totalWorkTime.value = data?.totalWorkTime ?? 0
      totalOverTime.value = data?.totalOverTime ?? 0
      totalPaidLeaveUsed.value = data?.totalPaidLeaveUsed ?? 0
      totalUnpaidLeaveUsed.value = data?.totalUnpaidLeaveUsed ?? 0
    }
    else {
      totalWorkingDays.value = 0
      totalAbsentDays.value = 0
      totalWorkTime.value = 0
      totalOverTime.value = 0
      totalPaidLeaveUsed.value = 0
      totalUnpaidLeaveUsed.value = 0
    }
  }
  catch (e) {
    logger.error(e)
  }
}

async function refreshAttendance() {
  await getAttendanceByDate()
  await summaryCardRef.value?.refreshSummary?.()
}

function onCancel() {
  modalVisible.value = false
}

onMounted(async () => {
  await Promise.all([getAttendanceByDate(), getSimpleProject(), getAttendanceEmployeeSummary()])
})

watch(() => props.pickedMonth, () => {
  selectedDay.value = props.pickedMonth ?? dayjs()

  getAttendanceByDate()
  getAttendanceEmployeeSummary()
})
</script>

<template>
  <div class="flex gap-4">
    <a-col span="17">
      <div ref="scheduleLeft" class="p-2 bg-white rounded-md">
        <EmployeeShiftTable v-model:selected-date="selectedDay" :data-source="attendanceDataByMonth"
          :type-of-sort="typeOfSort" :font-size="props.fontSize" :row-height="props.rowHeight"
          @refresh-attendance="refreshAttendance" />
      </div>
    </a-col>
    <a-col span="7">
      <SummaryCard ref="summaryCardRef" :selected-date="selectedDay" :calendar-left-height="scheduleLeftHeight"
        @refresh="refreshAttendance" />
    </a-col>
  </div>
  <a-modal v-model:open="modalVisible" :title="$t('monthlyAttendanceRequest')" width="300px"
    @ok="submitAttendanceToOffice" @cancel="onCancel">
    <a-form ref="formRef" :model="officeRequestFormstate">
      <a-form-item :label="$t('note')" name="projectId" :rules="[{ required: false }]" no-style>
        <a-textarea v-model:value="officeRequestFormstate.description" :placeholder="$t('note')" class="w-full" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
